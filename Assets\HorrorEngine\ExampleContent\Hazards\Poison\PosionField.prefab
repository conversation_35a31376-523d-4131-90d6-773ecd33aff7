%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &973447835854069142
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5703035607531013860}
  - component: {fileID: 6222726852529553066}
  - component: {fileID: 4470892936626491699}
  m_Layer: 9
  m_Name: PosionField
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5703035607531013860
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973447835854069142}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.83, y: 2.49, z: 8.55}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3856916516637824913}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6222726852529553066
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973447835854069142}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43c3d856a2845404c89872d1224020e3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Center: {x: 0, y: 0, z: 0}
  m_Size: {x: 10, y: 5, z: 10}
  m_LayerMask:
    serializedVersion: 2
    m_Bits: 512
  m_ShowDebug: 1
--- !u!114 &4470892936626491699
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973447835854069142}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4778bdf6ad4b1954a8f0bbea4928d254, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Attack: {fileID: 11400000, guid: a109e235255c26044baa36fb0b078061, type: 2}
  DamageRate: 0.5
  Duration: -1
  m_HitBox: {fileID: 0}
  m_PenetrationHits: 0
  m_CanReHitDamageable: 0
  OnAttackStart:
    m_PersistentCalls:
      m_Calls: []
  OnAttackEnd:
    m_PersistentCalls:
      m_Calls: []
--- !u!1001 &1570460168026075643
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 5703035607531013860}
    m_Modifications:
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: NoiseModule.enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.y.minMaxState
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.z.minMaxState
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: NoiseModule.strength.scalar
      value: 0.1
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.minMaxState
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: InitialModule.startSize.scalar
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: InitialModule.startSpeed.scalar
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: InitialModule.startSize.minScalar
      value: 0.25
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: EmissionModule.rateOverTime.scalar
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: InitialModule.startLifetime.scalar
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: InitialModule.startColor.maxColor.a
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: InitialModule.gravityModifier.scalar
      value: -0.0001
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: InitialModule.startLifetime.minScalar
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.size
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].time
      value: 0.24104118
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[2].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].value
      value: 0.9450737
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[0].inSlope
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].inSlope
      value: 0.6974046
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[2].inSlope
      value: -4.377385
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[0].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[0].outSlope
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].inWeight
      value: 0.33333334
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[1].outSlope
      value: 0.6974046
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[2].inWeight
      value: 0.15860619
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[2].outSlope
      value: -4.377385
      objectReference: {fileID: 0}
    - target: {fileID: -5468360627064605571, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: SizeModule.curve.maxCurve.m_Curve.Array.data[0].outWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2327762945361110634, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.77
      objectReference: {fileID: 0}
    - target: {fileID: 2327762945361110634, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.957
      objectReference: {fileID: 0}
    - target: {fileID: 2327762945361110634, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.65
      objectReference: {fileID: 0}
    - target: {fileID: 2327762945361110634, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2327762945361110634, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2327762945361110634, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2327762945361110634, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2327762945361110634, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2327762945361110634, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2327762945361110634, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5451758628510977846, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_Name
      value: Gas (1)
      objectReference: {fileID: 0}
    - target: {fileID: 7939019522193536547, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: be846dcbf64637c4aae1f6213c20e6d5, type: 2}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
--- !u!4 &3856916516637824913 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 2327762945361110634, guid: f076861b40e1582468ab13e8d7b8515b, type: 3}
  m_PrefabInstance: {fileID: 1570460168026075643}
  m_PrefabAsset: {fileID: 0}
