%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 58e5e9a2ec5a7634ebab86edd9f5f4aa, type: 3}
  m_Name: InkRibbon
  m_EditorClassIdentifier: 
  UniqueId: 8bcc9acf-d8f7-41ff-9f89-0f1a3a9711e0
  Image: {fileID: 21300000, guid: 771733391077fb84fbd9385b82277f47, type: 3}
  ExamineModel: {fileID: 4258714419210065941, guid: bf32083a6210d2e4aa1080f96714d8af, type: 3}
  Name:
    IsLocalized: 1
    Unlocalized: Ink Ribbon
    Localized:
      m_TableReference:
        m_TableCollectionName: GUID:7bb2b06d71f243b428e4ad9bf566ec1f
      m_TableEntryReference:
        m_KeyId: 347323387613184
        m_Key: 
      m_FallbackState: 0
      m_WaitForCompletion: 0
      m_LocalVariables: []
  Description:
    IsLocalized: 1
    Unlocalized: 'Using this on a typewriter will allow you to record your progress '
    Localized:
      m_TableReference:
        m_TableCollectionName: GUID:7bb2b06d71f243b428e4ad9bf566ec1f
      m_TableEntryReference:
        m_KeyId: 347561015906304
        m_Key: 
      m_FallbackState: 0
      m_WaitForCompletion: 0
      m_LocalVariables: []
  InventoryAction: 0
  Flags: 118
  DropPrefab: {fileID: 0}
  MaxStackSize: 0
  Name_DEPRECATED: 
  Description_DEPRECATED: 
  references:
    version: 2
    RefIds: []
