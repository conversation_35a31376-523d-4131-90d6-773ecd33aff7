fileFormatVersion: 2
guid: 480d5568b68a15c42a5486a43097b68b
ModelImporter:
  serializedVersion: 22200
  internalIDToNameTable: []
  externalObjects: {}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Idle
      takeName: rig|DummyIdle
      internalID: 2034363544457349556
      firstFrame: 0
      lastFrame: 240
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ReloadAssault
      takeName: rig|DummyReloadAssault
      internalID: 4960449980953990823
      firstFrame: 0
      lastFrame: 29
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ReloadGun
      takeName: rig|DummyReloadGun
      internalID: 6938608068421441778
      firstFrame: 0
      lastFrame: 23
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ReloadShotgun
      takeName: rig|DummyReloadShotgun
      internalID: -2254829930361668219
      firstFrame: 0
      lastFrame: 48
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Run
      takeName: rig|DummyRun
      internalID: -1815677418764871446
      firstFrame: 0
      lastFrame: 25
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events:
      - time: 0.16733068
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.3012834
        functionName: PlayVFX
        data: FootstepL
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.6321381
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.7715803
        functionName: PlayVFX
        data: FootstepR
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingGun
      takeName: rig|DummyShootingGun
      internalID: 6621324855862627422
      firstFrame: 0
      lastFrame: 8
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingGunDown
      takeName: rig|DummyShootingGunDown
      internalID: 3016394369382893037
      firstFrame: 0
      lastFrame: 8
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingGunUp
      takeName: rig|DummyShootingGunUp
      internalID: -5549254065589248882
      firstFrame: 0
      lastFrame: 8
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgun
      takeName: rig|DummyShootingShotgun
      internalID: 5427552734174306360
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgunDown
      takeName: rig|DummyShootingShotgunDown
      internalID: -4740119281907346971
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgunUp
      takeName: rig|DummyShootingShotgunUp
      internalID: 1531149649296935122
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TPose
      takeName: rig|DummyTPose
      internalID: -6154431903778946047
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Turn180
      takeName: rig|DummyTurn180
      internalID: 650913105093731497
      firstFrame: 0
      lastFrame: 41
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Walk
      takeName: rig|DummyWalk
      internalID: -3252149184052698072
      firstFrame: 0
      lastFrame: 25
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events:
      - time: 0.12749003
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.61885786
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingGunReady
      takeName: rig|DummyShootingGun
      internalID: 3249764584559746098
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 45
      level: 1
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 1
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingGunDownReady
      takeName: rig|DummyShootingGunDown
      internalID: 829900956900367650
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingGunUpReady
      takeName: rig|DummyShootingGunUp
      internalID: 6174098275459297560
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgunReady
      takeName: rig|DummyShootingShotgun
      internalID: 7850400647728622927
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgunDownReady
      takeName: rig|DummyShootingShotgunDown
      internalID: -7057727684719811028
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgunUpReady
      takeName: rig|DummyShootingShotgunUp
      internalID: -1726093034109078052
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: DEF-spine
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-thigh.L
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-thigh.R
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-shin.L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-shin.R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-foot.L
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-foot.R
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-spine.001
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-spine.002
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-shoulder.L
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-shoulder.R
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-upper_arm.L
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-upper_arm.R
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-forearm.L
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-forearm.R
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-hand.L
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-hand.R
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-toe.L
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-toe.R
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-spine.003
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: DummyAnimationsExport(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: rig
      parentName: DummyAnimationsExport(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071069, y: 0, z: -0, w: 0.7071067}
      scale: {x: 100, y: 100, z: 100}
    - name: root
      parentName: rig
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.000000029802322, y: -1.7699634e-15, z: 1.7650226e-15, w: 1}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: DEF-spine
      parentName: root
      position: {x: 0.00004212557, y: 0.0005519997, z: 0.010100206}
      rotation: {x: 0.7904554, y: 0.0000000010561371, z: -0.0000000014288575, w: 0.6125197}
      scale: {x: 1, y: 0.9999998, z: 0.9999998}
    - name: DEF-spine.001
      parentName: DEF-spine
      position: {x: -2.8751794e-11, y: 0.001522193, z: 3.6208803e-10}
      rotation: {x: -0.064476274, y: -2.4070353e-11, z: 4.5795585e-12, w: 0.9979192}
      scale: {x: 1.0000001, y: 0.99999994, z: 0.99999994}
    - name: DEF-spine.002
      parentName: DEF-spine.001
      position: {x: -4.6461824e-11, y: 0.0013663637, z: 2.989691e-10}
      rotation: {x: -0.07746411, y: 2.1940837e-10, z: 0.0000000021613655, w: 0.9969952}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: DEF-spine.003
      parentName: DEF-spine.002
      position: {x: 1.94359e-11, y: 0.0017288761, z: -3.3778633e-11}
      rotation: {x: 0.0016271025, y: -1.1722297e-10, z: -3.965122e-10, w: 0.9999987}
      scale: {x: 0.99999994, y: 0.99999976, z: 0.9999997}
    - name: DEF-neck
      parentName: DEF-spine.003
      position: {x: 2.806841e-10, y: 0.0019257873, z: 5.8767e-11}
      rotation: {x: 0.15626867, y: 2.3286104e-11, z: -3.9480094e-10, w: 0.9877146}
      scale: {x: 1, y: 0.9999999, z: 0.9999998}
    - name: DEF-head
      parentName: DEF-neck
      position: {x: -6.330083e-12, y: 0.0012828369, z: -2.1824234e-10}
      rotation: {x: -0.14214681, y: -1.9726795e-10, z: -0.0000000013736888, w: 0.9898456}
      scale: {x: 1, y: 1, z: 0.9999999}
    - name: DEF-head_end
      parentName: DEF-head
      position: {x: -1.9806698e-11, y: 0.0019830002, z: 1.1794202e-11}
      rotation: {x: -0, y: -2.2204458e-15, z: 0.0000000017763573, w: 1}
      scale: {x: 1, y: 1, z: 0.9999999}
    - name: DEF-head_end_end
      parentName: DEF-head_end
      position: {x: -0, y: 0.001983, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: DEF-shoulder.L
      parentName: DEF-spine.003
      position: {x: -0.00018299982, y: 0.0013722144, z: 0.0007825096}
      rotation: {x: -0.56595874, y: 0.40880004, z: 0.39755952, w: 0.59541565}
      scale: {x: 0.99999976, y: 0.9999999, z: 0.99999976}
    - name: DEF-upper_arm.L
      parentName: DEF-shoulder.L
      position: {x: -0.00007791562, y: 0.0020079094, z: -0.00020384799}
      rotation: {x: 0.11138036, y: -0.8023674, z: 0.14136605, w: 0.5690489}
      scale: {x: 0.9838002, y: 1.0332056, z: 0.9837998}
    - name: DEF-forearm.L
      parentName: DEF-upper_arm.L
      position: {x: 3.1315722e-10, y: 0.0028850993, z: -2.7939677e-11}
      rotation: {x: 0.03640204, y: 0.0044734767, z: -0.025905395, w: 0.9989914}
      scale: {x: 0.9984044, y: 1.0096685, z: 0.99173194}
    - name: DEF-hand.L
      parentName: DEF-forearm.L
      position: {x: 4.0512532e-10, y: 0.0026283546, z: -1.3969838e-10}
      rotation: {x: -0.018266052, y: 0.000601266, z: 0.03298597, w: 0.9992888}
      scale: {x: 1.0180691, y: 0.96384984, z: 1.0191875}
    - name: DEF-hand.L_end
      parentName: DEF-hand.L
      position: {x: 1.6298145e-11, y: 0.0008016073, z: 9.778887e-11}
      rotation: {x: -0.000000044703473, y: 0.000000012689267, z: -0.00000010726305, w: 1}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
    - name: DEF-hand.L_end_end
      parentName: DEF-hand.L_end
      position: {x: -0, y: 0.00080160797, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: DEF-shoulder.R
      parentName: DEF-spine.003
      position: {x: 0.00018300016, y: 0.0013722145, z: 0.0007825096}
      rotation: {x: -0.565959, y: -0.40879968, z: -0.39755946, w: 0.5954156}
      scale: {x: 0.9999997, y: 1.0000002, z: 0.9999999}
    - name: DEF-upper_arm.R
      parentName: DEF-shoulder.R
      position: {x: 0.00007791588, y: 0.0020079091, z: -0.00020384703}
      rotation: {x: -0.1113811, y: -0.8023672, z: 0.14136563, w: -0.56904906}
      scale: {x: 0.9838006, y: 1.0332055, z: 0.98379976}
    - name: DEF-forearm.R
      parentName: DEF-upper_arm.R
      position: {x: 8.1490724e-11, y: 0.0028851004, z: -7.4505804e-11}
      rotation: {x: 0.036402903, y: -0.0044732383, z: 0.02590456, w: 0.9989914}
      scale: {x: 0.9984041, y: 1.0096686, z: 0.99173224}
    - name: DEF-hand.R
      parentName: DEF-forearm.R
      position: {x: -1.405715e-10, y: 0.0026283544, z: -9.080395e-11}
      rotation: {x: -0.018266074, y: -0.00060132076, z: -0.03298579, w: 0.99928874}
      scale: {x: 1.0177221, y: 0.9631957, z: 1.0202794}
    - name: DEF-hand.R_end
      parentName: DEF-hand.R
      position: {x: -1.8626451e-11, y: 0.00080160826, z: -5.5879353e-11}
      rotation: {x: -0.0000000018626445, y: -0.000000021886073, z: 0.000000046427868, w: 1}
      scale: {x: 1.0000006, y: 1.0000006, z: 1}
    - name: DEF-hand.R_end_end
      parentName: DEF-hand.R_end
      position: {x: -0, y: 0.00080160797, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: DEF-thigh.L
      parentName: DEF-spine
      position: {x: -0.0009799998, y: 0.0007081826, z: 0.0002594231}
      rotation: {x: 0.9865149, y: 0.0000000017724826, z: 2.6176821e-11, w: 0.16367142}
      scale: {x: 0.99889344, y: 1.002217, z: 0.998891}
    - name: DEF-shin.L
      parentName: DEF-thigh.L
      position: {x: 1.4370016e-10, y: 0.0053636944, z: 2.7939677e-11}
      rotation: {x: 0.08754098, y: -2.2171263e-10, z: 3.103995e-11, w: 0.9961609}
      scale: {x: 0.9997985, y: 1.000478, z: 0.9997259}
    - name: DEF-foot.L
      parentName: DEF-shin.L
      position: {x: -1.523972e-10, y: 0.0045421473, z: -1.1168595e-11}
      rotation: {x: -0.5273625, y: 0.00049741566, z: -0.0003087202, w: 0.8496402}
      scale: {x: 1.0008603, y: 1.0010093, z: 0.9981367}
    - name: DEF-toe.L
      parentName: DEF-foot.L
      position: {x: 1.5006663e-11, y: 0.0012924535, z: 2.7939677e-11}
      rotation: {x: 0.00016141988, y: 0.9612484, z: -0.27568305, w: 0.0005627075}
      scale: {x: 1.0004575, y: 1.0011704, z: 0.99837935}
    - name: DEF-toe.L_end
      parentName: DEF-toe.L
      position: {x: -2.0081643e-11, y: 0.0006720001, z: -3.929017e-12}
      rotation: {x: -0.000000029816373, y: 2.3283062e-10, z: -0.000000028434439, w: 1}
      scale: {x: 1.0000004, y: 1.0000004, z: 1.0000002}
    - name: DEF-toe.L_end_end
      parentName: DEF-toe.L_end
      position: {x: -0, y: 0.00067200005, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: DEF-thigh.R
      parentName: DEF-spine
      position: {x: 0.0009799999, y: 0.0007081835, z: 0.00025942246}
      rotation: {x: 0.9865149, y: 0.0000000017728631, z: -1.9676184e-10, w: 0.16367142}
      scale: {x: 0.99889356, y: 1.0022163, z: 0.9988906}
    - name: DEF-shin.R
      parentName: DEF-thigh.R
      position: {x: -5.0263224e-11, y: 0.0053636944, z: 5.820766e-13}
      rotation: {x: 0.087541044, y: 1.3872056e-11, z: -5.883072e-13, w: 0.996161}
      scale: {x: 0.9997985, y: 1.000552, z: 0.99965143}
    - name: DEF-foot.R
      parentName: DEF-shin.R
      position: {x: 1.8398395e-10, y: 0.004542147, z: 6.1845637e-12}
      rotation: {x: -0.5273626, y: 0.00049740303, z: -0.00030873256, w: 0.8496402}
      scale: {x: 1.0008523, y: 1.0011272, z: 0.99802685}
    - name: DEF-toe.R
      parentName: DEF-foot.R
      position: {x: -1.4149919e-10, y: 0.0012924537, z: 3.637979e-12}
      rotation: {x: 0.00016151779, y: 0.9612484, z: -0.2756832, w: 0.00056270784}
      scale: {x: 1.000457, y: 1.0012233, z: 0.9983265}
    - name: DEF-toe.R_end
      parentName: DEF-toe.R
      position: {x: 4.640398e-11, y: 0.00067200005, z: 3.7717596e-11}
      rotation: {x: -0.00000011927403, y: -0.000000031315714, z: -0.00000011084191, w: 1}
      scale: {x: 1.0000004, y: 1.0000002, z: 1.0000002}
    - name: DEF-toe.R_end_end
      parentName: DEF-toe.R_end
      position: {x: -0, y: 0.00067200005, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Dummy
      parentName: DummyAnimationsExport(Clone)
      position: {x: 0.000000001169212, y: -4.296683e-10, z: 0.00000000863178}
      rotation: {x: 0.70710665, y: 2.4985437e-15, z: 5.3449935e-19, w: 0.7071069}
      scale: {x: 0.01, y: 0.01, z: 0.010000001}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  importBlendShapeDeformPercent: 0
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
