{"name": "PlayerActions", "maps": [{"name": "Gameplay", "id": "1a069275-2311-418b-86bc-3ba2654c91ad", "actions": [{"name": "PrimaryAxis", "type": "Value", "id": "f2ce915b-5034-40f4-8e5e-d819bb21bc62", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "SecondaryAxis", "type": "Value", "id": "040a8aed-2f1f-4ec1-8d19-5b5b54362f52", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Aiming", "type": "<PERSON><PERSON>", "id": "0de53bd7-a553-4809-bbe5-f04e1539377d", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Attack", "type": "<PERSON><PERSON>", "id": "53776148-10fe-4da8-8a0c-ac038a91ae96", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Interact", "type": "<PERSON><PERSON>", "id": "4abdcee8-2cf8-4250-a3ce-a6fb646a9414", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Run", "type": "<PERSON><PERSON>", "id": "1b6bde2c-4744-4e14-b1ab-4b883666f365", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Reload", "type": "<PERSON><PERSON>", "id": "6f891c5c-d12e-44c8-9c4a-29eb2d27e01b", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Turn180", "type": "<PERSON><PERSON>", "id": "ba528feb-46d9-4074-b36e-de03a749112e", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Change<PERSON>imTarget", "type": "<PERSON><PERSON>", "id": "c329c4e0-5300-42b0-a917-83af72cd01b4", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "60e49a61-8ad9-4897-9e3c-cdb06ef27e28", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "", "groups": "Gamepad", "action": "PrimaryAxis", "isComposite": false, "isPartOfComposite": false}, {"name": "2D Vector", "id": "ac5ac73c-3031-46f8-9b17-e116011f9909", "path": "2DVector(mode=1)", "interactions": "", "processors": "StickDeadzone", "groups": "", "action": "PrimaryAxis", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "a55210a5-c946-40eb-b3bb-3bf25984fd71", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "KeyboardAndMouse", "action": "PrimaryAxis", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "ce710fd8-333b-40d7-903a-9d987d8292f7", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "KeyboardAndMouse", "action": "PrimaryAxis", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "82ab7d94-5989-4166-858f-ef0a29a7943b", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "KeyboardAndMouse", "action": "PrimaryAxis", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "b4dcec8b-e18c-412c-aa95-5081e03f8a65", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "KeyboardAndMouse", "action": "PrimaryAxis", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "b62ae2f8-1e2f-4609-9683-b664764c5aef", "path": "<Gamepad>/dpad", "interactions": "", "processors": "", "groups": "Gamepad", "action": "PrimaryAxis", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "aa201482-6b2d-4ace-82d5-9ed92d5685db", "path": "<Gamepad>/rightShoulder", "interactions": "Press(behavior=2)", "processors": "", "groups": "Gamepad", "action": "Aiming", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "20b9b819-bef0-491b-abbc-fd9ecaab7040", "path": "<Mouse>/rightButton", "interactions": "Press(behavior=2)", "processors": "", "groups": "KeyboardAndMouse", "action": "Aiming", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7325e8aa-5157-4599-bf33-85d8620183fc", "path": "<Gamepad>/leftTrigger", "interactions": "Press(behavior=2)", "processors": "", "groups": "Gamepad", "action": "Aiming", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "409b287c-e4fc-4a97-ac36-75001049b626", "path": "<Gamepad>/buttonSouth", "interactions": "Press(behavior=2)", "processors": "", "groups": "Gamepad", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a8ff6a58-033f-4765-9a71-aaf65eea4268", "path": "<Mouse>/leftButton", "interactions": "Press(behavior=2)", "processors": "", "groups": "KeyboardAndMouse", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0be28395-045f-4749-9397-62b3aa2826f0", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9ee441cc-afd6-4768-8bc9-71dec90cbcfa", "path": "<Keyboard>/e", "interactions": "", "processors": "", "groups": "KeyboardAndMouse", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "874ab57b-6b7d-496c-861c-264098805525", "path": "<Keyboard>/leftShift", "interactions": "Press(behavior=2)", "processors": "", "groups": "KeyboardAndMouse", "action": "Run", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4482426c-55cc-4085-9f35-f5ceea9eb227", "path": "<Gamepad>/buttonWest", "interactions": "Press(behavior=2)", "processors": "", "groups": "Gamepad", "action": "Run", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9e5f31d7-4b53-47dd-8391-96af9b05040a", "path": "<Keyboard>/r", "interactions": "", "processors": "", "groups": "KeyboardAndMouse", "action": "Reload", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "43c19787-c49b-4c52-bb32-e38eec25676a", "path": "<Gamepad>/buttonWest", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Reload", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "24036118-36b9-46a2-a561-749d05fe03e9", "path": "<Gamepad>/rightTrigger", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Turn180", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "567c7f39-24d0-41d0-90fb-19bf4bcc4142", "path": "<Keyboard>/q", "interactions": "", "processors": "", "groups": "KeyboardAndMouse", "action": "Turn180", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f60b24df-da33-4ced-89d9-5948763ebe90", "path": "<Keyboard>/tab", "interactions": "", "processors": "", "groups": "KeyboardAndMouse", "action": "Change<PERSON>imTarget", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3accccb2-e546-465c-ac76-29bc21e28e0e", "path": "<Gamepad>/leftShoulder", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Change<PERSON>imTarget", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c162c421-78fa-4aa3-b782-db88a2d86fc5", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "", "groups": "Gamepad", "action": "SecondaryAxis", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "47d207bf-c367-429d-a5b8-b4b7b3c4aca1", "path": "<Mouse>/delta", "interactions": "", "processors": "", "groups": "KeyboardAndMouse", "action": "SecondaryAxis", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}, {"name": "KeyboardAndMouse", "bindingGroup": "KeyboardAndMouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}]}