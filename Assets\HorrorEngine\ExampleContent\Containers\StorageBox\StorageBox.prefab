%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1001 &6818507853032140570
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 6643833081680686489}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.52271
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.52271
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.52271
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.035
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.70710784
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70710576
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8140792372716624907, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483647
      objectReference: {fileID: 0}
    - target: {fileID: -3363159317952646968, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -3363159317952646968, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -3363159317952646968, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -3363159317952646968, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -3363159317952646968, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_Name
      value: StorageBox
      objectReference: {fileID: 0}
    - target: {fileID: 5881580285822398446, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 66dbc4cab8cdd474497ca5e309e5eee8, type: 2}
    - target: {fileID: 8233725748283174921, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 66dbc4cab8cdd474497ca5e309e5eee8, type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
--- !u!1 &5883439149913279727 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -8140792372716624907, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
  m_PrefabInstance: {fileID: 6818507853032140570}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &4237252752345430208
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5883439149913279727}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 3.0079544, y: 1.8409084, z: 2.175164}
  m_Center: {x: 0, y: -0.0795457, z: 0.087582044}
--- !u!1 &5936128132019655243 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 7a73bed6e5d9408419d5fc025a6cf061, type: 3}
  m_PrefabInstance: {fileID: 6818507853032140570}
  m_PrefabAsset: {fileID: 0}
--- !u!95 &1366816753
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5936128132019655243}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 62d42fd791bf7f143ad90794c64db8a7, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 2
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
--- !u!1001 &6818507853468176953
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_OpenClip
      value: 
      objectReference: {fileID: 8300000, guid: e4a345ba69305ef43bb07a8ad3b415b4, type: 3}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_CloseClip
      value: 
      objectReference: {fileID: 8300000, guid: 06b2f8827e168674485745524444e0f7, type: 3}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 1366816753}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 1366816753}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetTrigger
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetTrigger
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: UnityEngine.Animator, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: UnityEngine.Animator, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: Open
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: Close
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 10924464049888487, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 185657181020782498, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
      propertyPath: m_Name
      value: StorageBox
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
--- !u!4 &6643833081680686489 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 185657181020782496, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
  m_PrefabInstance: {fileID: 6818507853468176953}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &6643833081680686491 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 185657181020782498, guid: f57d97aa1cefadc4e9deb4ca324bae57, type: 3}
  m_PrefabInstance: {fileID: 6818507853468176953}
  m_PrefabAsset: {fileID: 0}
--- !u!82 &929581775
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6643833081680686491}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 2996514446778832361, guid: ad249dd62c6ba77419b1e31c269fb3e9, type: 2}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 1
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
