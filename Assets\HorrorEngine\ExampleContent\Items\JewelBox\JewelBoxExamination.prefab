%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1001 &782073686370929165
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1165426184458968993}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.30328405
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.30328405
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.30328405
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071078
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.70710576
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 907953707714485536, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: f6a33e6281c080a4cb53cec47c007d10, type: 2}
    - target: {fileID: 919132149155446097, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_Name
      value: JewelBox
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2444279819765676538, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 7395900685784602029, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 7395900685784602029, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8565240970504464338, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8565240970504464338, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8565240970504464338, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8565240970504464338, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8565240970504464338, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8711950208204629585, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: f6a33e6281c080a4cb53cec47c007d10, type: 2}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2875032625758509248}
    - targetCorrespondingSourceObject: {fileID: 2444279819765676538, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      insertIndex: -1
      addedObject: {fileID: 9200753263330722334}
    - targetCorrespondingSourceObject: {fileID: 7395900685784602029, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4649323493021699678}
  m_SourcePrefab: {fileID: 100100000, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
--- !u!1 &439965240681887580 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
  m_PrefabInstance: {fileID: 782073686370929165}
  m_PrefabAsset: {fileID: 0}
--- !u!95 &2875032625758509248
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 439965240681887580}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 4dc44934ede11b84f9fe2cad9596cf74, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 2
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!4 &959480724739370470 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
  m_PrefabInstance: {fileID: 782073686370929165}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &3112453898310792183 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2444279819765676538, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
  m_PrefabInstance: {fileID: 782073686370929165}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &9200753263330722334
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3112453898310792183}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 3.44372, y: 1.1423335, z: 3.969555}
  m_Center: {x: 0, y: 0.097752154, z: 0}
--- !u!1 &7816559703186710432 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7395900685784602029, guid: 5ac97e96e484cb6409c367e10492cac3, type: 3}
  m_PrefabInstance: {fileID: 782073686370929165}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &4649323493021699678
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7816559703186710432}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 3.44372, y: 1.3004777, z: 3.969555}
  m_Center: {x: -1.7004877, y: 0.6191579, z: 0}
--- !u!1001 &939725590399709214
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1165426184458968993}
    m_Modifications:
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.7613233
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.39723366
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.48475602
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.005
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.057
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.003
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2128693019522745467, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_Name
      value: InteractionKey
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.size
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_Mode
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_Mode
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 1189003593355605093}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 7849712888684500253}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_Target
      value: 
      objectReference: {fileID: 1165426184458968992}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_Target
      value: 
      objectReference: {fileID: 1165426184458968992}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_Target
      value: 
      objectReference: {fileID: 1165426184458968992}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetActive
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: SetActive
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_MethodName
      value: RemoveItemFromPlayer
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_MethodName
      value: GiveItemToPlayer
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_MethodName
      value: CloseExamination
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: UnityEngine.GameObject, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: UnityEngine.GameObject, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_TargetAssemblyTypeName
      value: HorrorEngine.UIExamineItemUtility, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_TargetAssemblyTypeName
      value: HorrorEngine.UIExamineItemUtility, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_TargetAssemblyTypeName
      value: HorrorEngine.UIExamineItemUtility, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_Arguments.m_BoolArgument
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_ObjectArgument
      value: 
      objectReference: {fileID: 11400000, guid: c50dbf6ac5b642d44bd8505f4f84df7f, type: 2}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_StringArgument
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_Arguments.m_ObjectArgument
      value: 
      objectReference: {fileID: 11400000, guid: fba335b76854b654b95e141b01f79646, type: 2}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_Arguments.m_StringArgument
      value: OnKeyPickedUp
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: HorrorEngine.ItemData, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: HorrorEngine.ItemData, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
--- !u!4 &818500851564847274 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
  m_PrefabInstance: {fileID: 939725590399709214}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1189003593355605093 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2128693019522745467, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
  m_PrefabInstance: {fileID: 939725590399709214}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4558607112908707144
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1165426184458968993}
    m_Modifications:
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.19610666
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.19610666
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.19610666
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.03
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.501
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2128693019522745467, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: m_Name
      value: InteractionOpen
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.size
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 2875032625758509248}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2506761174050132275}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_Target
      value: 
      objectReference: {fileID: 9009654880733733537}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_Target
      value: 
      objectReference: {fileID: 1165426184458968992}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_Target
      value: 
      objectReference: {fileID: 1165426184458968992}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetTrigger
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: SetActive
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_MethodName
      value: Play
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_MethodName
      value: CloseExamination
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_MethodName
      value: CloseExamination
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: UnityEngine.Animator, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: UnityEngine.GameObject, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_TargetAssemblyTypeName
      value: UnityEngine.AudioSource, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_TargetAssemblyTypeName
      value: HorrorEngine.UIExamineItemUtility, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_TargetAssemblyTypeName
      value: HorrorEngine.UIExamineItemUtility, Assembly-CSharp
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: Open
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_ObjectArgument
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_Arguments.m_ObjectArgument
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[3].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 7192505850423417541, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
      propertyPath: OnInteract.m_PersistentCalls.m_Calls.Array.data[4].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
--- !u!1 &2506761174050132275 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2128693019522745467, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
  m_PrefabInstance: {fileID: 4558607112908707144}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &4112376099885860348 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 455273963870671028, guid: c10c3cc2aac81144a8b7872c3df610a0, type: 3}
  m_PrefabInstance: {fileID: 4558607112908707144}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6930654558206388300
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1165426184458968993}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.5320222
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.5320222
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.5320222
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.107
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.005
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.075
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.61237377
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.61237186
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.35355335
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.35355228
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 60
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: a4f329f362470234484b57a7dc93982f, type: 2}
    - target: {fileID: 919132149155446097, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_Name
      value: KeyLow
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
--- !u!4 &7468121452913889191 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
  m_PrefabInstance: {fileID: 6930654558206388300}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &7849712888684500253 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: b7e2692e36dfe7a4987bc7edbb053062, type: 3}
  m_PrefabInstance: {fileID: 6930654558206388300}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8220767802110250867
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 7078079930702860496, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_Name
      value: JewelBoxExamination
      objectReference: {fileID: 0}
    - target: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4112376099885860348}
    - targetCorrespondingSourceObject: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      insertIndex: -1
      addedObject: {fileID: 818500851564847274}
    - targetCorrespondingSourceObject: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      insertIndex: -1
      addedObject: {fileID: 959480724739370470}
    - targetCorrespondingSourceObject: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7468121452913889191}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 7078079930702860496, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      insertIndex: -1
      addedObject: {fileID: 9009654880733733537}
    - targetCorrespondingSourceObject: {fileID: 7078079930702860496, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7974786629031899547}
  m_SourcePrefab: {fileID: 100100000, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
--- !u!114 &1165426184458968992 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7078079930702860499, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
  m_PrefabInstance: {fileID: 8220767802110250867}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165426184458968995}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 803f0896e80ecd34ea75ca6c1638d449, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &1165426184458968993 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7078079930702860498, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
  m_PrefabInstance: {fileID: 8220767802110250867}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1165426184458968995 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7078079930702860496, guid: 4d827492396d6fb4f9cf523a4bb98263, type: 3}
  m_PrefabInstance: {fileID: 8220767802110250867}
  m_PrefabAsset: {fileID: 0}
--- !u!82 &9009654880733733537
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165426184458968995}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 2996514446778832361, guid: ad249dd62c6ba77419b1e31c269fb3e9, type: 2}
  m_audioClip: {fileID: 8300000, guid: 56afe44241d32d44793c9a46e5bb33e5, type: 3}
  m_Resource: {fileID: 8300000, guid: 56afe44241d32d44793c9a46e5bb33e5, type: 3}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &7974786629031899547
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165426184458968995}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4fc15ddf559fcac478f4209b170f7bfe, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ResetOnEnable: 1
  OnReset:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 2506761174050132275}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
      - m_Target: {fileID: 1189003593355605093}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
      - m_Target: {fileID: 7849712888684500253}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
