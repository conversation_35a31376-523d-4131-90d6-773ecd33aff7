Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.10f1 (3c681a6c22ff) revision 3958810'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 32676 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-05T20:12:39Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
C:/Projects/Unity/ProjectCourt
-logFile
Logs/AssetImportWorker4.log
-srvPort
64442
-job-worker-count
15
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Projects/Unity/ProjectCourt
C:/Projects/Unity/ProjectCourt
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19796]  Target information:

Player connection [19796]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1206875916 [EditorId] 1206875916 [Version] 1048832 [Id] WindowsEditor(7,Bastian) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19796] Host joined multi-casting on [***********:54997]...
Player connection [19796] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 15
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.10f1 (3c681a6c22ff)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Projects/Unity/ProjectCourt/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        AMD Radeon RX 6800 XT (ID=0x73bf)
    Vendor:          ATI
    VRAM:            16338 MB
    App VRAM Budget: 15570 MB
    Driver:          32.0.21025.1024
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56496
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002570 seconds.
- Loaded All Assemblies, in  0.353 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 318 ms
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.703 seconds
Domain Reload Profiling: 1054ms
	BeginReloadAssembly (117ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (149ms)
		LoadAssemblies (115ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (145ms)
			TypeCache.Refresh (144ms)
				TypeCache.ScanAssembly (132ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (703ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (659ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (442ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (107ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.735 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.92 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.674 seconds
Domain Reload Profiling: 1406ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (510ms)
		LoadAssemblies (336ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (257ms)
			TypeCache.Refresh (187ms)
				TypeCache.ScanAssembly (166ms)
			BuildScriptInfoCaches (49ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (675ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (504ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (308ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 243 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7866 unused Assets / (4.6 MB). Loaded Objects now: 8618.
Memory consumption went from 184.2 MB to 179.6 MB.
Total: 9.241500 ms (FindLiveObjects: 0.937200 ms CreateObjectMapping: 0.491600 ms MarkObjects: 4.884700 ms  DeleteObjects: 2.926200 ms)

========================================================================
Received Import Request.
  Time since last request: 45000.273735 seconds.
  path: Assets/HorrorEngine/ExampleContent/Audio/HE_GameStart.wav
  artifactKey: Guid(30fba25bcb3c7254daae7b1738ed104b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Audio/HE_GameStart.wav using Guid(30fba25bcb3c7254daae7b1738ed104b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9f05113c71316c9ee9701dab9a3e66db') in 0.0827739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.776980 seconds.
  path: Assets/HorrorEngine/ExampleContent/Cinematics/CinematicRT.renderTexture
  artifactKey: Guid(3f9f848ac9f944f498fd2f1b50a4a97f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Cinematics/CinematicRT.renderTexture using Guid(3f9f848ac9f944f498fd2f1b50a4a97f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd2400d3c380ce75a3edee00e72a87876') in 0.0215536 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1.053002 seconds.
  path: Assets/HorrorEngine/ExampleContent/Databases/DocumentDB.asset
  artifactKey: Guid(30ccee90e9fe6074fbbe73a9d1add4a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Databases/DocumentDB.asset using Guid(30ccee90e9fe6074fbbe73a9d1add4a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '95e5e4fb85d0b3f741b71955eb14a681') in 0.0098536 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.202601 seconds.
  path: Assets/HorrorEngine/ExampleContent/Databases/MapDB.asset
  artifactKey: Guid(c423acb542438c44db88c2dc29c811fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Databases/MapDB.asset using Guid(c423acb542438c44db88c2dc29c811fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '683085451d63a776e87e169b27c25f0d') in 0.0042722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.567619 seconds.
  path: Assets/HorrorEngine/ExampleContent/Documents/DemoDocument.asset
  artifactKey: Guid(75b456d23dcf44045af4c34059cd2437) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Documents/DemoDocument.asset using Guid(75b456d23dcf44045af4c34059cd2437) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '390ddd9411552b2f8297a93a770d9478') in 0.0051953 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/HorrorEngine/ExampleContent/Documents/DigiLogPage.wav
  artifactKey: Guid(c3849fa0dcaf3cf40b74686dbf1766ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Documents/DigiLogPage.wav using Guid(c3849fa0dcaf3cf40b74686dbf1766ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a0cf70161a8a5d197e0442a3b3a84bad') in 0.0175727 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 2.345498 seconds.
  path: Assets/HorrorEngine/ExampleContent/Footsteps/FootstepBlood.wav
  artifactKey: Guid(0934a8a34fea3bc488c42832e4d40064) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Footsteps/FootstepBlood.wav using Guid(0934a8a34fea3bc488c42832e4d40064) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c8c0d7b24117cd58ee76156445adc8e9') in 0.0163882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 2.520437 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.scenetemplate
  artifactKey: Guid(8cb4b5eb3e9b0aa47bc6aec33d491f1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.scenetemplate using Guid(8cb4b5eb3e9b0aa47bc6aec33d491f1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'b6dcddfc458fd045b2b33ca17845004c') in 0.6539409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7462

========================================================================
Received Import Request.
  Time since last request: 1.972495 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.unity
  artifactKey: Guid(024c00e974906ec47a44d2299644729e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.unity using Guid(024c00e974906ec47a44d2299644729e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2c0efcc5a2f9f8b064c3e7f6f4da0ed6') in 0.000493 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 3.344946 seconds.
  path: Assets/HorrorEngine/ExampleContent/Textures/grid.png
  artifactKey: Guid(8dcb97f39b431fc4da1dcb6851cc9d52) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Textures/grid.png using Guid(8dcb97f39b431fc4da1dcb6851cc9d52) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '06a427eec31c8b75d39ea97780f842d4') in 0.0139623 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.646 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.604 seconds
Domain Reload Profiling: 1250ms
	BeginReloadAssembly (194ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (389ms)
		LoadAssemblies (293ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (172ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (150ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (605ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (460ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (285ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 22 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7875 unused Assets / (5.4 MB). Loaded Objects now: 8654.
Memory consumption went from 157.4 MB to 152.0 MB.
Total: 12.489000 ms (FindLiveObjects: 0.951900 ms CreateObjectMapping: 1.266800 ms MarkObjects: 5.987100 ms  DeleteObjects: 4.281700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 261.172971 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/SampleScene/LightingData.asset
  artifactKey: Guid(f75205077e7977246a89d192ccf0d88a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/SampleScene/LightingData.asset using Guid(f75205077e7977246a89d192ccf0d88a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Lighting data asset ‘LightingData’ is incompatible with the current Unity version because the scene it was baked for was not serialized. Please use Generate Lighting to rebuild the lighting data, or assign the target scene to the Lighting Data Asset in the inspector.
 -> (artifact id: '846717cb279b59987ddbad4083096809') in 0.019724 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.310285 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.scenetemplate
  artifactKey: Guid(8cb4b5eb3e9b0aa47bc6aec33d491f1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.scenetemplate using Guid(8cb4b5eb3e9b0aa47bc6aec33d491f1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'a34e8b6b2542a030760900a2f64a3c8a') in 0.6944842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7462

========================================================================
Received Import Request.
  Time since last request: 1.652676 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/SampleScene1.unity
  artifactKey: Guid(63a0e531f639fad44b0397aafbded078) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/SampleScene1.unity using Guid(63a0e531f639fad44b0397aafbded078) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2891061050a4b7b72a7a35f09abfa4c4') in 0.0006042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.652 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.638 seconds
Domain Reload Profiling: 1290ms
	BeginReloadAssembly (192ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (397ms)
		LoadAssemblies (298ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (177ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (154ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (638ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (107ms)
			ProcessInitializeOnLoadAttributes (297ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7874 unused Assets / (5.4 MB). Loaded Objects now: 8659.
Memory consumption went from 155.6 MB to 150.2 MB.
Total: 13.830100 ms (FindLiveObjects: 1.148000 ms CreateObjectMapping: 1.215200 ms MarkObjects: 6.519200 ms  DeleteObjects: 4.945500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 77.527113 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.scenetemplate
  artifactKey: Guid(8cb4b5eb3e9b0aa47bc6aec33d491f1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.scenetemplate using Guid(8cb4b5eb3e9b0aa47bc6aec33d491f1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 1.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '830d0124870c9af51839394d5e98353e') in 0.9478048 seconds
Number of updated asset objects reloaded before import = 5Number of asset objects unloaded after import = 7469

========================================================================
Received Import Request.
  Time since last request: 1.271394 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.scenetemplate
  artifactKey: Guid(8cb4b5eb3e9b0aa47bc6aec33d491f1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.scenetemplate using Guid(8cb4b5eb3e9b0aa47bc6aec33d491f1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '0c3f1f012d6cebc4359fa1fdbfc132d2') in 0.577488 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7473

========================================================================
Received Import Request.
  Time since last request: 39.768921 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/SampleScene2.unity
  artifactKey: Guid(1a84b7a8bcad7974385f66f205b0a474) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/SampleScene2.unity using Guid(1a84b7a8bcad7974385f66f205b0a474) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '464bfbb71769436c9043a86f1063f5ae') in 0.0005676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.192108 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/SamplePuzzleKeypad.unity
  artifactKey: Guid(ae67899ece7469f48a0ee99939df55e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/SamplePuzzleKeypad.unity using Guid(ae67899ece7469f48a0ee99939df55e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aea4cbfacbeb0a164c4fb0e5bf8fea05') in 0.0005487 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.569557 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/SamplePlatformingObstacles.unity
  artifactKey: Guid(8c19344bd35df104ebf5f8534ebbdbed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/SamplePlatformingObstacles.unity using Guid(8c19344bd35df104ebf5f8534ebbdbed) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8d8fe1b43270562e7bd1debf4e36d7a8') in 0.0005677 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.276125 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/SamplePlatformingLadders.unity
  artifactKey: Guid(96baae04020fa5d40a18e65dc1f46d34) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/SamplePlatformingLadders.unity using Guid(96baae04020fa5d40a18e65dc1f46d34) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff38b9e537861ab3d972232f521ab41c') in 0.0005829 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.692 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.14 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.642 seconds
Domain Reload Profiling: 1334ms
	BeginReloadAssembly (194ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (434ms)
		LoadAssemblies (325ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (643ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (495ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (117ms)
			ProcessInitializeOnLoadAttributes (305ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 27 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7888 unused Assets / (6.9 MB). Loaded Objects now: 8667.
Memory consumption went from 160.0 MB to 153.1 MB.
Total: 11.744000 ms (FindLiveObjects: 1.100100 ms CreateObjectMapping: 0.919200 ms MarkObjects: 5.569300 ms  DeleteObjects: 4.154600 ms)

Prepare: number of updated asset objects reloaded= 0
