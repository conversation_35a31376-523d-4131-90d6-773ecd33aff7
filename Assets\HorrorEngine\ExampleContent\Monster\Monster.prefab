%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &375890416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 375890417}
  - component: {fileID: 375890419}
  - component: {fileID: 1058344192193805519}
  - component: {fileID: 4876107122666643299}
  m_Layer: 9
  m_Name: Attack
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &375890417
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 375890416}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.513, z: 0.478}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 15899562054687983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &375890419
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 375890416}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4778bdf6ad4b1954a8f0bbea4928d254, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Attack: {fileID: 11400000, guid: 058e2a2c1a006964cac0eeb73d81f872, type: 2}
  OnAttackStart:
    m_PersistentCalls:
      m_Calls: []
  OnAttackStop:
    m_PersistentCalls:
      m_Calls: []
  DamageRate: 0
  Duration: 0
  m_HitShape: {fileID: 0}
  m_PenetrationHits: 0
  StartAttackOnStart: 1
  m_CanReHitDamageable: 0
  OnImpact:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1058344192193805519
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 375890416}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43c3d856a2845404c89872d1224020e3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Center: {x: 0, y: 0, z: -0.15}
  m_Size: {x: 1, y: 1, z: 1.5}
  m_LayerMask:
    serializedVersion: 2
    m_Bits: 512
  m_ShowDebug: 0
--- !u!114 &4876107122666643299
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 375890416}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 481abaa30e72ebd46a480d1e482cc146, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Attack: {fileID: 375890419}
  CountConsumption: 1
  Duration: 1
  MontageDelay: 0
  AttackActivationDelay: 0.5
  Animation: {fileID: 11400000, guid: 02ebd4999d3040b46a196a90c0adbcaa, type: 2}
  AnimationBlendTime: 0.2
  m_AnimEventCallbacks: []
  m_AudioSource: {fileID: 0}
  m_PreDelaySoundWillStart: {fileID: 0}
  m_PreDelaySoundWillNotStart: {fileID: 0}
  m_AttackSound: {fileID: 0}
  m_IsAttackSoundLoop: 0
  m_AttackCompleteSound: {fileID: 0}
  m_AttackNotStartedSound: {fileID: 0}
  m_AttackInterruptedSound: {fileID: 0}
  OnPreDelayWillStart:
    m_PersistentCalls:
      m_Calls: []
  OnPreDelayWillNotStart:
    m_PersistentCalls:
      m_Calls: []
  OnPlay:
    m_PersistentCalls:
      m_Calls: []
  OnComplete:
    m_PersistentCalls:
      m_Calls: []
  OnInterrupt:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &386964088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 386964090}
  - component: {fileID: 386964089}
  - component: {fileID: 386964093}
  - component: {fileID: 386964092}
  - component: {fileID: 386964091}
  m_Layer: 0
  m_Name: GrabFrontAttack
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &386964090
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 386964088}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.513, z: 0.583}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1034065898}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &386964089
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 386964088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bd478d5f22e835b4b8b121c24129cc35, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AnimationState: {fileID: 11400000, guid: fe34855e70a13784cb62c63d697c475f, type: 2}
  m_AnimationBlendTime: 0
  m_EnterStateTransitions: []
  m_ExitAnimationState: {fileID: 0}
  m_ExitAnimationBlendTime: 0
  m_ExitAnimationDuration: 0
  m_AnimEventCallbacks: []
  m_AnimationOverride: {fileID: 0}
  SkipPreviousExitAnimation: 0
  OnStateEnter:
    m_PersistentCalls:
      m_Calls: []
  OnStateExit:
    m_PersistentCalls:
      m_Calls: []
  Tags: []
  m_GrabDistance: 1
  m_GrabTag: GrabbedFront
  m_ExitState: {fileID: 1051151729}
  m_FacingSpeed: 1
  m_GrabDelay: 0.25
  m_AttackDelay: 1
  m_AttackDelayRandomOffset: 0.25
  m_ApproachSpeedMultiplier: 1
  m_Cooldown: 3
  m_OnGrabAttack: {fileID: 0}
  m_HitShape: {fileID: 0}
  m_MinAngleOfEntry: 0
  m_MaxAngleOfEntry: 90
  m_GrabPositioning:
    Type: 0
    Distance: 0.6
    MoveSpeed: 2
  m_RotateTowardsTarget: 1
--- !u!114 &386964093
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 386964088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43c3d856a2845404c89872d1224020e3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Center: {x: 0, y: 0, z: 0}
  m_Size: {x: 0.5, y: 1, z: 2}
  m_LayerMask:
    serializedVersion: 2
    m_Bits: 512
  m_ShowDebug: 0
--- !u!114 &386964092
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 386964088}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4778bdf6ad4b1954a8f0bbea4928d254, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Attack: {fileID: 11400000, guid: f6afa4e7cc95fd540bcc71ddefe9b96b, type: 2}
  OnAttackStart:
    m_PersistentCalls:
      m_Calls: []
  OnAttackStop:
    m_PersistentCalls:
      m_Calls: []
  DamageRate: 0.5
  Duration: 0
  m_HitShape: {fileID: 0}
  m_PenetrationHits: 0
  StartAttackOnStart: 1
  m_CanReHitDamageable: 0
  OnImpact:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &386964091
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 386964088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 481abaa30e72ebd46a480d1e482cc146, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Attack: {fileID: 386964092}
  CountConsumption: 1
  Duration: 1.14
  MontageDelay: 0
  AttackActivationDelay: 0.75
  Animation: {fileID: 11400000, guid: 095d83ecb51559247912ce874f9018a9, type: 2}
  AnimationBlendTime: 0.2
  m_AnimEventCallbacks: []
  m_AudioSource: {fileID: 0}
  m_PreDelaySoundWillStart: {fileID: 0}
  m_PreDelaySoundWillNotStart: {fileID: 0}
  m_AttackSound: {fileID: 0}
  m_IsAttackSoundLoop: 0
  m_AttackCompleteSound: {fileID: 0}
  m_AttackNotStartedSound: {fileID: 0}
  m_AttackInterruptedSound: {fileID: 0}
  OnPreDelayWillStart:
    m_PersistentCalls:
      m_Calls: []
  OnPreDelayWillNotStart:
    m_PersistentCalls:
      m_Calls: []
  OnPlay:
    m_PersistentCalls:
      m_Calls: []
  OnComplete:
    m_PersistentCalls:
      m_Calls: []
  OnInterrupt:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &800970005
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 800970006}
  - component: {fileID: 800970007}
  m_Layer: 0
  m_Name: ToHurt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &800970006
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 800970005}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1496381347}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &800970007
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 800970005}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a1ae61185a8e60844af782949de63443, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FromAllStates: 1
  m_FromStates: []
  m_ExcludeStates: []
  m_ToState: {fileID: 2035331328}
--- !u!1 &850916264
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 850916265}
  - component: {fileID: 850916266}
  m_Layer: 0
  m_Name: Idle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &850916265
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 850916264}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1034065898}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &850916266
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 850916264}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f0df989f68c3b0741a23004822bbef4b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AnimationState: {fileID: 11400000, guid: 33e4667e46a616847a2cd82618ba665d, type: 2}
  m_AnimationBlendTime: 0.25
  m_EnterStateTransitions: []
  m_ExitAnimationState: {fileID: 0}
  m_ExitAnimationBlendTime: 0.25
  m_ExitAnimationDuration: 0
  m_AnimEventCallbacks: []
  m_AnimationOverride: {fileID: 0}
  SkipPreviousExitAnimation: 0
  OnStateEnter:
    m_PersistentCalls:
      m_Calls: []
  OnStateExit:
    m_PersistentCalls:
      m_Calls: []
  Tags: []
  m_AlertedState: {fileID: 1051151729}
  m_WanderState: {fileID: 0}
  TimeBetweenWander: 3
--- !u!1 &946777552
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 946777553}
  - component: {fileID: 946777554}
  m_Layer: 0
  m_Name: HeadStabbed
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &946777553
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 946777552}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1034065898}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &946777554
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 946777552}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fa71fea094ade2b4496a6ed8443b6e58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AnimationState: {fileID: 11400000, guid: 2d92a12f0797c824a966a57f4c1eec41, type: 2}
  m_AnimationBlendTime: 0.25
  m_EnterStateTransitions: []
  m_ExitAnimationState: {fileID: 0}
  m_ExitAnimationBlendTime: 0.25
  m_ExitAnimationDuration: 0
  m_AnimEventCallbacks: []
  m_AnimationOverride: {fileID: 0}
  SkipPreviousExitAnimation: 0
  OnStateEnter:
    m_PersistentCalls:
      m_Calls: []
  OnStateExit:
    m_PersistentCalls:
      m_Calls: []
  Tags:
  - HeadStabbed
--- !u!1 &1034065897
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1034065898}
  m_Layer: 0
  m_Name: States
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1034065898
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1034065897}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 850916265}
  - {fileID: 1051151728}
  - {fileID: 2145126331}
  - {fileID: 2035331327}
  - {fileID: 1829046820}
  - {fileID: 386964090}
  - {fileID: 1871727645}
  - {fileID: 2086994644}
  - {fileID: 946777553}
  m_Father: {fileID: 15899562054687983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1049850588
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1049850589}
  - component: {fileID: **********}
  m_Layer: 0
  m_Name: ToDeath
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1049850589
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1049850588}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1496381347}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &**********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1049850588}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a1ae61185a8e60844af782949de63443, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FromAllStates: 1
  m_FromStates: []
  m_ExcludeStates: []
  m_ToState: {fileID: 286146302}
--- !u!1 &1051151727
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1051151728}
  - component: {fileID: 1051151729}
  m_Layer: 0
  m_Name: Alerted
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1051151728
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1051151727}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1034065898}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1051151729
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1051151727}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bdd20d2d1fb44f543a294f3faf8801b0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AnimationState: {fileID: 11400000, guid: 38297b17bd6196f4baed590a4f40ed8d, type: 2}
  m_AnimationBlendTime: 0.25
  m_EnterStateTransitions:
  - FromState: {fileID: 2145126332}
    AnimationState: {fileID: 11400000, guid: cc48124fab29cf74fb7f21f65398d20e, type: 2}
    AnimationBlendTime: 0.25
  - FromState: {fileID: 2035331328}
    AnimationState: {fileID: 11400000, guid: cc48124fab29cf74fb7f21f65398d20e, type: 2}
    AnimationBlendTime: 0.25
  - FromState: {fileID: 386964089}
    AnimationState: {fileID: 11400000, guid: cc48124fab29cf74fb7f21f65398d20e, type: 2}
    AnimationBlendTime: 0.25
  - FromState: {fileID: 1871727649}
    AnimationState: {fileID: 11400000, guid: cc48124fab29cf74fb7f21f65398d20e, type: 2}
    AnimationBlendTime: 0.25
  m_ExitAnimationState: {fileID: 0}
  m_ExitAnimationBlendTime: 0.25
  m_ExitAnimationDuration: 0
  m_AnimEventCallbacks: []
  m_AnimationOverride: {fileID: 0}
  SkipPreviousExitAnimation: 0
  OnStateEnter:
    m_PersistentCalls:
      m_Calls: []
  OnStateExit:
    m_PersistentCalls:
      m_Calls: []
  Tags: []
  m_Duration: 0
  m_ExitState: {fileID: 0}
  m_IdleState: {fileID: 850916266}
  m_AttackStates:
  - {fileID: 2145126332}
  m_GrabStates:
  - {fileID: 386964089}
  - {fileID: 1871727649}
  m_InitialDelay: 0
  m_MinTimeBetweenAttacks: 1
  m_FacingSpeedBetweenAttacks: 1
  m_ShowDebug: 0
--- !u!1 &1496381346
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1496381347}
  m_Layer: 0
  m_Name: GlobalTransitions
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1496381347
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1496381346}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 800970006}
  - {fileID: 1049850589}
  m_Father: {fileID: 15899562054687983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1829046818
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1829046820}
  - component: {fileID: 286146302}
  m_Layer: 0
  m_Name: Death
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1829046820
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1829046818}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1034065898}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &286146302
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1829046818}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 86a29e64eafc0e24aae8d250f34e142d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AnimationState: {fileID: 11400000, guid: 90dccce87e2784d4abc0e6393c39c834, type: 2}
  m_AnimationBlendTime: 0.25
  m_EnterStateTransitions:
  - FromState: {fileID: 850916266}
    AnimationState: {fileID: 11400000, guid: f4c845536b602af4299412e642295728, type: 2}
    AnimationBlendTime: 0.25
  m_ExitAnimationState: {fileID: 0}
  m_ExitAnimationBlendTime: 0.25
  m_ExitAnimationDuration: 0
  m_AnimEventCallbacks: []
  m_AnimationOverride: {fileID: 0}
  SkipPreviousExitAnimation: 0
  OnStateEnter:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 8843059744144794403}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 6785860290527581881}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 375890416}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 1956677757365882997}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 1951775240}
        m_TargetAssemblyTypeName: UnityEngine.Collider, UnityEngine
        m_MethodName: set_enabled
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  OnStateExit:
    m_PersistentCalls:
      m_Calls: []
  Tags: []
  m_DamageableSpecificAnimation:
  - Damageable: {fileID: 2109145054904827736}
    AnimationState: {fileID: 11400000, guid: 75cb33d7cf65bd145a5eab6c77592dec, type: 2}
--- !u!1 &1871727644
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1871727645}
  - component: {fileID: 1871727649}
  - component: {fileID: 1871727648}
  - component: {fileID: 1871727647}
  - component: {fileID: 1871727646}
  m_Layer: 0
  m_Name: GrabBacktAttack
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1871727645
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1871727644}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.513, z: 0.583}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1034065898}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1871727649
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1871727644}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bd478d5f22e835b4b8b121c24129cc35, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AnimationState: {fileID: 11400000, guid: fe34855e70a13784cb62c63d697c475f, type: 2}
  m_AnimationBlendTime: 0
  m_EnterStateTransitions: []
  m_ExitAnimationState: {fileID: 0}
  m_ExitAnimationBlendTime: 0
  m_ExitAnimationDuration: 0
  m_AnimEventCallbacks: []
  m_AnimationOverride: {fileID: 0}
  SkipPreviousExitAnimation: 0
  OnStateEnter:
    m_PersistentCalls:
      m_Calls: []
  OnStateExit:
    m_PersistentCalls:
      m_Calls: []
  Tags: []
  m_GrabDistance: 1
  m_GrabTag: GrabbedBack
  m_ExitState: {fileID: 1051151729}
  m_FacingSpeed: 1
  m_GrabDelay: 0
  m_AttackDelay: 1
  m_AttackDelayRandomOffset: 0.25
  m_ApproachSpeedMultiplier: 1
  m_Cooldown: 3
  m_OnGrabAttack: {fileID: 0}
  m_HitShape: {fileID: 0}
  m_MinAngleOfEntry: 90
  m_MaxAngleOfEntry: 360
  m_GrabPositioning:
    Type: 0
    Distance: 0.6
    MoveSpeed: 2
  m_RotateTowardsTarget: 1
--- !u!114 &1871727648
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1871727644}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43c3d856a2845404c89872d1224020e3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Center: {x: 0, y: 0, z: 0}
  m_Size: {x: 0.5, y: 1, z: 2}
  m_LayerMask:
    serializedVersion: 2
    m_Bits: 512
  m_ShowDebug: 0
--- !u!114 &1871727647
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1871727644}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4778bdf6ad4b1954a8f0bbea4928d254, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Attack: {fileID: 11400000, guid: f6afa4e7cc95fd540bcc71ddefe9b96b, type: 2}
  OnAttackStart:
    m_PersistentCalls:
      m_Calls: []
  OnAttackStop:
    m_PersistentCalls:
      m_Calls: []
  DamageRate: 0.5
  Duration: 0
  m_HitShape: {fileID: 0}
  m_PenetrationHits: 0
  StartAttackOnStart: 1
  m_CanReHitDamageable: 0
  OnImpact:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1871727646
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1871727644}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 481abaa30e72ebd46a480d1e482cc146, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Attack: {fileID: 1871727647}
  CountConsumption: 1
  Duration: 1
  MontageDelay: 0
  AttackActivationDelay: 0.5
  Animation: {fileID: 11400000, guid: 095d83ecb51559247912ce874f9018a9, type: 2}
  AnimationBlendTime: 0.2
  m_AnimEventCallbacks: []
  m_AudioSource: {fileID: 0}
  m_PreDelaySoundWillStart: {fileID: 0}
  m_PreDelaySoundWillNotStart: {fileID: 0}
  m_AttackSound: {fileID: 0}
  m_IsAttackSoundLoop: 0
  m_AttackCompleteSound: {fileID: 0}
  m_AttackNotStartedSound: {fileID: 0}
  m_AttackInterruptedSound: {fileID: 0}
  OnPreDelayWillStart:
    m_PersistentCalls:
      m_Calls: []
  OnPreDelayWillNotStart:
    m_PersistentCalls:
      m_Calls: []
  OnPlay:
    m_PersistentCalls:
      m_Calls: []
  OnComplete:
    m_PersistentCalls:
      m_Calls: []
  OnInterrupt:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &2035331326
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2035331327}
  - component: {fileID: 2035331328}
  m_Layer: 0
  m_Name: Hurt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2035331327
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2035331326}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1034065898}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2035331328
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2035331326}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 998423764eaa23247b853b48b4b4eb42, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AnimationState: {fileID: 11400000, guid: 2d92a12f0797c824a966a57f4c1eec41, type: 2}
  m_AnimationBlendTime: 0
  m_EnterStateTransitions:
  - FromState: {fileID: 850916266}
    AnimationState: {fileID: 11400000, guid: 3146a61ec1e88d646b5c241e0dfe516b, type: 2}
    AnimationBlendTime: 0
  m_ExitAnimationState: {fileID: 0}
  m_ExitAnimationBlendTime: 0.25
  m_ExitAnimationDuration: 0
  m_AnimEventCallbacks: []
  m_AnimationOverride: {fileID: 0}
  SkipPreviousExitAnimation: 0
  OnStateEnter:
    m_PersistentCalls:
      m_Calls: []
  OnStateExit:
    m_PersistentCalls:
      m_Calls: []
  Tags: []
  m_Duration: 0
  m_ExitState: {fileID: 1051151729}
--- !u!1 &2086994643
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2086994644}
  - component: {fileID: 2086994645}
  m_Layer: 0
  m_Name: Knockback
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2086994644
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2086994643}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1034065898}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2086994645
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2086994643}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c5b1b65776881f4cbd31389f888dc91, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AnimationState: {fileID: 11400000, guid: 3146a61ec1e88d646b5c241e0dfe516b, type: 2}
  m_AnimationBlendTime: 0.25
  m_EnterStateTransitions: []
  m_ExitAnimationState: {fileID: 0}
  m_ExitAnimationBlendTime: 0.25
  m_ExitAnimationDuration: 0
  m_AnimEventCallbacks: []
  m_AnimationOverride: {fileID: 0}
  SkipPreviousExitAnimation: 0
  OnStateEnter:
    m_PersistentCalls:
      m_Calls: []
  OnStateExit:
    m_PersistentCalls:
      m_Calls: []
  Tags:
  - Knockback
  m_Duration: 0
  m_ExitState: {fileID: 1051151729}
  m_Force: 8
  m_Drag: 0.075
--- !u!1 &2145126330
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2145126331}
  - component: {fileID: 2145126332}
  m_Layer: 0
  m_Name: Attack
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2145126331
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2145126330}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1034065898}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2145126332
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2145126330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 192c70a39481f404fa72e416c7c8902e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AnimationState: {fileID: 0}
  m_AnimationBlendTime: 0
  m_EnterStateTransitions: []
  m_ExitAnimationState: {fileID: 0}
  m_ExitAnimationBlendTime: 0
  m_ExitAnimationDuration: 0
  m_AnimEventCallbacks: []
  m_AnimationOverride: {fileID: 0}
  SkipPreviousExitAnimation: 0
  OnStateEnter:
    m_PersistentCalls:
      m_Calls: []
  OnStateExit:
    m_PersistentCalls:
      m_Calls: []
  Tags: []
  m_Duration: 1
  m_ExitState: {fileID: 850916266}
  m_AlertedState: {fileID: 1051151729}
  m_FacingSpeed: 1
  m_AttackMontage: {fileID: 4876107122666643299}
  m_RotateTowardsTarget: 1
  AttackDistance: 1
  Cooldown: 3
--- !u!1 &15899562054687987
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 15899562054687983}
  - component: {fileID: 15899562054687981}
  - component: {fileID: 497740278}
  - component: {fileID: 497740277}
  - component: {fileID: 297726881}
  - component: {fileID: 297726879}
  - component: {fileID: 297726880}
  - component: {fileID: 7725017355750191088}
  - component: {fileID: 6703070634942282287}
  - component: {fileID: 3849763396253215832}
  - component: {fileID: 6581720336968615096}
  - component: {fileID: 1951775228}
  - component: {fileID: 1951775240}
  - component: {fileID: 2843694252710096315}
  - component: {fileID: 49904382}
  - component: {fileID: 2843436566164568490}
  - component: {fileID: -8586246839482920696}
  m_Layer: 0
  m_Name: Monster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &15899562054687983
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3355971567369888097}
  - {fileID: 230401356465601050}
  - {fileID: 375890417}
  - {fileID: 859611************}
  - {fileID: 5148835191181761702}
  - {fileID: 1034065898}
  - {fileID: 1453231634}
  - {fileID: 1496381347}
  - {fileID: 1727203295955038758}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &15899562054687981
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ec3a046b976ab440aac443f4b308f2d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Infinite: 0
  Invulnerable: 0
  Max: 3
  Min: 0
  Value: 3
  InitialValue: 0
  OnHealthAltered:
    m_PersistentCalls:
      m_Calls: []
  OnHealthDecreased:
    m_PersistentCalls:
      m_Calls: []
  OnDeath:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: **********}
        m_TargetAssemblyTypeName: HorrorEngine.ActorStateTransition, Assembly-CSharp
        m_MethodName: Trigger
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 2843436566164568490}
        m_TargetAssemblyTypeName: HorrorEngine.StatusEffectHandler, Assembly-CSharp
        m_MethodName: ClearAllStatusEffects
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  OnLoadedDead:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 15899562054687987}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!54 &497740278
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!195 &497740277
NavMeshAgent:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_AgentTypeID: 0
  m_Radius: 0.34
  m_Speed: 1
  m_Acceleration: 100
  avoidancePriority: 50
  m_AngularSpeed: 1000
  m_StoppingDistance: 1
  m_AutoTraverseOffMeshLink: 1
  m_AutoBraking: 0
  m_AutoRepath: 1
  m_Height: 2
  m_BaseOffset: 0
  m_WalkableMask: 4294967295
  m_ObstacleAvoidanceType: 4
--- !u!114 &297726881
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f15be47fe4d013f4289946a7272ccbbd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Handle: {fileID: 0}
  MainAnimator: {fileID: 1266772116042581724}
  OnTeleported:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &297726879
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ba8f37b33ef1e7b4ba0e0c1b070d1eaa, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InitialState: {fileID: 850916266}
  m_ShowDebug: 0
  OnStateChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &297726880
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c24a66b1c4ae6474884fb7d7be88f29b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Senses: []
  m_FindSensesInChildren: 1
  OnPlayerDetected:
    m_PersistentCalls:
      m_Calls: []
  OnPlayerLost:
    m_PersistentCalls:
      m_Calls: []
  OnPlayerReacheable:
    m_PersistentCalls:
      m_Calls: []
  OnPlayerUnreachable:
    m_PersistentCalls:
      m_Calls: []
  m_ShowDebug: 0
--- !u!114 &7725017355750191088
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e3d2f32b296a8d341a65960adc8c5e02, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TargetMask:
    serializedVersion: 2
    m_Bits: 64
  m_SightBlockerMask:
    serializedVersion: 2
    m_Bits: 1
  m_SightPoint: {fileID: 859611************}
  m_MaxDistance: 100
  m_Offset: {x: 0, y: 0, z: 0}
--- !u!114 &6703070634942282287
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9291977c9f2a14a5099c6a295b1a2eee, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Id: 344d28f2-029f-4ed6-80fc-d938f2475eab
  IsUniqueInstance: 0
--- !u!114 &3849763396253215832
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70caf85336dfd4b369928545b052d32e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ApplySavedTransform: 1
--- !u!114 &6581720336968615096
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6c76476fa31542d4090eeff43df19a68, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_OffsetUp: 1
  m_Distance: 2
  m_GroundCheckLayerMask:
    serializedVersion: 2
    m_Bits: 1
  OnGroundChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1951775228
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 613a86a84d150cb41bbb8b9a307e0e0c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Faction: {fileID: 11400000, guid: 146901eb6558c5e4885bb551465a164d, type: 2}
--- !u!136 &1951775240
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.24
  m_Height: 2.19
  m_Direction: 1
  m_Center: {x: 0, y: 1.18, z: 0}
--- !u!114 &2843694252710096315
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3cab56a05b016fb44a03849deb3d6420, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &49904382
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5b4a82e3b6ae1214e92d9474394870bc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &2843436566164568490
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0a2b2326cf7ecf4cb0ac503d563992e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!95 &-8586246839482920696
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 15899562054687987}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 555a50f62cd11cb40b4bf03ee887ff1a, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &605680202671566789
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3620766039736324084}
  - component: {fileID: 4318591422590353211}
  m_Layer: 0
  m_Name: SenseTargetPlayer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3620766039736324084
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 605680202671566789}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1727203295955038758}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4318591422590353211
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 605680202671566789}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a63f46903ad6fd045a01acd91c32b850, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1213644622888160588
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6932862754206278381}
  - component: {fileID: 4620703648149473689}
  m_Layer: 0
  m_Name: SenseGrabbed
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6932862754206278381
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1213644622888160588}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1727203295955038758}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4620703648149473689
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1213644622888160588}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9b8c731851d40864ba0bce0dc95c755a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TickFrequency: 0.5
  m_Target: {fileID: 4318591422590353211}
--- !u!1 &1956677757365882997
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1727203295955038758}
  m_Layer: 0
  m_Name: Senses
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1727203295955038758
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1956677757365882997}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6616640137332873208}
  - {fileID: 3116023214654713153}
  - {fileID: 5973759083582468463}
  - {fileID: 3620766039736324084}
  - {fileID: 7673625252940236730}
  - {fileID: 7009008598033164886}
  - {fileID: 6932862754206278381}
  m_Father: {fileID: 15899562054687983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2535922792987407937
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7673625252940236730}
  - component: {fileID: 7215758181690331566}
  m_Layer: 0
  m_Name: SenseVitability
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7673625252940236730
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2535922792987407937}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1727203295955038758}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7215758181690331566
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2535922792987407937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e8f63645f846f4c46911731f1410cdfd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TickFrequency: 0.5
  m_Target: {fileID: 4318591422590353211}
--- !u!1 &4136655274158019950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6616640137332873208}
  - component: {fileID: 8904396936432462831}
  m_Layer: 0
  m_Name: SenseSight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6616640137332873208
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4136655274158019950}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1727203295955038758}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8904396936432462831
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4136655274158019950}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a4869e7b2ac4b474d96f82bbe0f9b7fd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TickFrequency: 0
  m_Target: {fileID: 4318591422590353211}
--- !u!1 &4991117951096412633
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5973759083582468463}
  - component: {fileID: 1407878766467463806}
  m_Layer: 0
  m_Name: SenseReachability
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5973759083582468463
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4991117951096412633}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1727203295955038758}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1407878766467463806
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4991117951096412633}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 27d492877a7e9504fb03860b0a0754a7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TickFrequency: 0.5
  m_Target: {fileID: 4318591422590353211}
--- !u!1 &5479745063849269236
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3116023214654713153}
  - component: {fileID: 2773636652706761947}
  m_Layer: 0
  m_Name: SenseProximity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3116023214654713153
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5479745063849269236}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1727203295955038758}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2773636652706761947
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5479745063849269236}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b1676872b202be240b7779f3b6309834, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TickFrequency: 0.5
  m_Target: {fileID: 4318591422590353211}
  m_DetectionRadius: 10
  m_UndetectionRadius: 15
--- !u!1 &6785860290527581881
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 230401356465601050}
  - component: {fileID: 2326678696746187228}
  - component: {fileID: 2109145054904827736}
  - component: {fileID: 204076679}
  - component: {fileID: 5886877338147623014}
  m_Layer: 9
  m_Name: DamageableHead
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &230401356465601050
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6785860290527581881}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 2.186, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 15899562054687983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &2326678696746187228
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6785860290527581881}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.25
  m_Height: 0.5
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &2109145054904827736
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6785860290527581881}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f82791e57d8266478186984ade90ab0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: {fileID: 11400000, guid: e4177163a99ab4847a091e592739c85b, type: 2}
  OnPreDamage:
    m_PersistentCalls:
      m_Calls: []
  OnDamage:
    m_PersistentCalls:
      m_Calls: []
  Priority: 1
--- !u!114 &204076679
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6785860290527581881}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3eafa02a1ebd7df43824c652e40d3a14, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ImpactVFX: {fileID: 7644617639541797170, guid: 6208ad60afc9346469dda731fb75aa4c, type: 3}
  m_ImpactVFXPosition: 0
  OnlyOnDeath: 0
--- !u!114 &5886877338147623014
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6785860290527581881}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3eafa02a1ebd7df43824c652e40d3a14, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ImpactVFX: {fileID: 7644617639541797170, guid: 4d67c636e84153c42bba2353c9476a60, type: 3}
  m_ImpactVFXPosition: 1
  OnlyOnDeath: 1
--- !u!1 &7867676710325577807
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7009008598033164886}
  - component: {fileID: 5764825131207269430}
  m_Layer: 0
  m_Name: SenseDamage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7009008598033164886
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7867676710325577807}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1727203295955038758}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5764825131207269430
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7867676710325577807}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 80946957a97f0c345abf20a91b3406b1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TickFrequency: 0
--- !u!1 &8181253647878205209
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 859611************}
  m_Layer: 0
  m_Name: SightPoint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &859611************
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8181253647878205209}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.795, z: 0.256}
  m_LocalScale: {x: 0.62042654, y: 0.62042654, z: 0.62042654}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 15899562054687983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8843059744144794403
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3355971567369888097}
  - component: {fileID: 2431941646154466981}
  - component: {fileID: 8492893238926711060}
  - component: {fileID: 4588013433635842959}
  m_Layer: 9
  m_Name: DamageableMain
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3355971567369888097
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8843059744144794403}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.923, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 15899562054687983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &2431941646154466981
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8843059744144794403}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.25
  m_Height: 1.52
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &8492893238926711060
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8843059744144794403}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f82791e57d8266478186984ade90ab0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: {fileID: 11400000, guid: fdbbcf8df8fab944dac4cedfe107cc9f, type: 2}
  OnPreDamage:
    m_PersistentCalls:
      m_Calls: []
  OnDamage:
    m_PersistentCalls:
      m_Calls: []
  Priority: 0
--- !u!114 &4588013433635842959
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8843059744144794403}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 435c904868da911468193b752e684abd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  VisibilityTracePoints:
  - {x: 0, y: 0, z: 0}
  - {x: 0, y: 1, z: 0}
--- !u!1001 &4683443647114787661
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 15899562054687983}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.6
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.6
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.6
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7425383180895326887, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_CastShadows
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7425383180895326887, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0fccec8fa7a87be4f848fd3c5b8de93f, type: 2}
    - target: {fileID: 919132149155446097, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_Name
      value: Monster (1)
      objectReference: {fileID: 0}
    - target: {fileID: 5866666021909216657, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      propertyPath: m_Controller
      value: 
      objectReference: {fileID: 9100000, guid: 555a50f62cd11cb40b4bf03ee887ff1a, type: 2}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      insertIndex: -1
      addedObject: {fileID: 317051708}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      insertIndex: -1
      addedObject: {fileID: 5128524897366102197}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7251729311147834748}
    - targetCorrespondingSourceObject: {fileID: 7781156350450842681, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
      insertIndex: -1
      addedObject: {fileID: 5069029713961493594}
  m_SourcePrefab: {fileID: 100100000, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
--- !u!95 &1266772116042581724 stripped
Animator:
  m_CorrespondingSourceObject: {fileID: 5866666021909216657, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
  m_PrefabInstance: {fileID: 4683443647114787661}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &3099276265160673140 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7781156350450842681, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
  m_PrefabInstance: {fileID: 4683443647114787661}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &5069029713961493594
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3099276265160673140}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3376cc107ccc50c42a19fd847b3acec0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Handle: {fileID: 11400000, guid: 5fb5c2d13d47ea6468c0af4739a398e4, type: 2}
  m_GizmoSize: 0.25
--- !u!4 &5148835191181761702 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
  m_PrefabInstance: {fileID: 4683443647114787661}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &5494258501194286620 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
  m_PrefabInstance: {fileID: 4683443647114787661}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &317051708
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5494258501194286620}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a80d7188085d7034eab4500bd6844b66, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SocketController: {fileID: 0}
  m_VisualEffects:
  - Identifier: GrabBlood
    Selector: {fileID: 0}
    Effect: {fileID: 7644617639541797170, guid: 6208ad60afc9346469dda731fb75aa4c, type: 3}
    InstantiationSettings:
      Parent: {fileID: 8529819941775413709}
      Socket: {fileID: 0}
      Position: {x: 0, y: 0, z: 0}
      Rotation: {x: 0, y: 0, z: 0}
      Scale: 0
      IsLocal: 1
      InheritsRotation: 1
      DetachFromParent: 1
--- !u!114 &5128524897366102197
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5494258501194286620}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 32db8a6cef6bbf64d9f10e70ee9768b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PropertyName: Speed
  m_InterpolationSpeed: 20
  OptionalForwardReference: {fileID: 0}
  m_TeleportationThreshold: 1
--- !u!114 &7251729311147834748
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5494258501194286620}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 73f0aacf9f9ad9f4f88b7bbe685c0135, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  OnEvent:
    m_PersistentCalls:
      m_Calls: []
--- !u!4 &8529819941775413709 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -5287528166829118848, guid: 33d9799511d6d4d48b7f50eceae495bd, type: 3}
  m_PrefabInstance: {fileID: 4683443647114787661}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &5719252999135928365
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 15899562054687983}
    m_Modifications:
    - target: {fileID: 3002289915860704636, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_GroundDetector
      value: 
      objectReference: {fileID: 6581720336968615096}
    - target: {fileID: 5719253000519364156, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_Name
      value: Blob
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_RootOrder
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: b487e30c11d3d7542855608445849cac, type: 3}
--- !u!4 &1453231634 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5719253000519364159, guid: b487e30c11d3d7542855608445849cac, type: 3}
  m_PrefabInstance: {fileID: 5719252999135928365}
  m_PrefabAsset: {fileID: 0}
