%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 468a46d0ae32c3544b7d98094e6448a9, type: 3}
  m_Name: AddressableAssetSettings
  m_EditorClassIdentifier: 
  m_DefaultGroup: de3934439f8ff06428b279def704f304
  m_currentHash:
    serializedVersion: 2
    Hash: f77eb6647736f749cb6f61acc0e452d0
  m_OptimizeCatalogSize: 0
  m_BuildRemoteCatalog: 0
  m_CatalogRequestsTimeout: 0
  m_DisableCatalogUpdateOnStart: 0
  m_InternalIdNamingMode: 0
  m_InternalBundleIdMode: 1
  m_AssetLoadMode: 0
  m_BundledAssetProviderType:
    m_AssemblyName: Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    m_ClassName: UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider
  m_AssetBundleProviderType:
    m_AssemblyName: Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    m_ClassName: UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider
  m_IgnoreUnsupportedFilesInBuild: 0
  m_UniqueBundleIds: 0
  m_EnableJsonCatalog: 0
  m_NonRecursiveBuilding: 1
  m_CCDEnabled: 0
  m_maxConcurrentWebRequests: 3
  m_UseUWRForLocalBundles: 0
  m_BundleTimeout: 0
  m_BundleRetryCount: 0
  m_BundleRedirectLimit: -1
  m_SharedBundleSettings: 0
  m_SharedBundleSettingsCustomGroupIndex: 0
  m_ContiguousBundles: 1
  m_StripUnityVersionFromBundleBuild: 0
  m_DisableVisibleSubAssetRepresentations: 0
  m_BuiltInBundleNaming: 0
  mBuiltInBundleCustomNaming: 
  m_MonoScriptBundleNaming: 0
  m_CheckForContentUpdateRestrictionsOption: 0
  m_MonoScriptBundleCustomNaming: 
  m_RemoteCatalogBuildPath:
    m_Id: 
  m_RemoteCatalogLoadPath:
    m_Id: 
  m_ContentStateBuildPathProfileVariableName: 
  m_CustomContentStateBuildPath: 
  m_ContentStateBuildPath: 
  m_BuildAddressablesWithPlayerBuild: 0
  m_overridePlayerVersion: '[UnityEditor.PlayerSettings.bundleVersion]'
  m_GroupAssets:
  - {fileID: 11400000, guid: 7f5fab9c1c3ea86459b006bcfc6747ab, type: 2}
  - {fileID: 11400000, guid: 680f9f34953233647a61d2f1d7341c1d, type: 2}
  - {fileID: 11400000, guid: ff5287c2900794e489d737cad2224aa8, type: 2}
  - {fileID: 11400000, guid: e0e32d2de726de24fb983870c04754c8, type: 2}
  - {fileID: 11400000, guid: 68efce61c1ea89341a6d63a12f6559e6, type: 2}
  - {fileID: 11400000, guid: 30ca84c8ff8f33645afa8789fc44867c, type: 2}
  - {fileID: 11400000, guid: 37a80df99e993364a837ba3c7a39d12f, type: 2}
  m_BuildSettings:
    m_LogResourceManagerExceptions: 1
    m_BundleBuildPath: Temp/com.unity.addressables/AssetBundles
  m_ProfileSettings:
    m_Profiles:
    - m_InheritedParent: 
      m_Id: 7805f1f1ad5511943a2504643c963ddf
      m_ProfileName: Default
      m_Values:
      - m_Id: 2d124553e4260ca40b9fefa9a52e988f
        m_Value: 'ServerData/[BuildTarget]'
      - m_Id: ae85d59ff30ec94479e1fdf7600f0a6c
        m_Value: <undefined>
      - m_Id: bbfa5c895fd3e45488b0c9c1946c624b
        m_Value: '[UnityEngine.AddressableAssets.Addressables.BuildPath]/[BuildTarget]'
      - m_Id: c152c94f9314cc549860c20da453a115
        m_Value: '{UnityEngine.AddressableAssets.Addressables.RuntimePath}/[BuildTarget]'
      - m_Id: e9aa4d52eb5479c4cbd8abf98d27338e
        m_Value: '[UnityEditor.EditorUserBuildSettings.activeBuildTarget]'
    m_ProfileEntryNames:
    - m_Id: 2d124553e4260ca40b9fefa9a52e988f
      m_Name: Remote.BuildPath
      m_InlineUsage: 0
    - m_Id: ae85d59ff30ec94479e1fdf7600f0a6c
      m_Name: Remote.LoadPath
      m_InlineUsage: 0
    - m_Id: bbfa5c895fd3e45488b0c9c1946c624b
      m_Name: Local.BuildPath
      m_InlineUsage: 0
    - m_Id: c152c94f9314cc549860c20da453a115
      m_Name: Local.LoadPath
      m_InlineUsage: 0
    - m_Id: e9aa4d52eb5479c4cbd8abf98d27338e
      m_Name: BuildTarget
      m_InlineUsage: 0
    m_ProfileVersion: 1
  m_LabelTable:
    m_LabelNames:
    - default
    - Locale
    - Locale-en
    - Locale-es
    - Locale-fr
    - Locale-de
  m_SchemaTemplates: []
  m_GroupTemplateObjects:
  - {fileID: 11400000, guid: 4e076aabd54a72d43842d7732bc83088, type: 2}
  m_InitializationObjects: []
  m_CertificateHandlerType:
    m_AssemblyName: 
    m_ClassName: 
  m_ActivePlayerDataBuilderIndex: 2
  m_DataBuilders:
  - {fileID: 11400000, guid: 1bdeda2f9859e984b88883753752395e, type: 2}
  - {fileID: 11400000, guid: 5a4ce6cbe42af38419ad6adc1a90d0ad, type: 2}
  - {fileID: 11400000, guid: 3cb2e0cb0ebe1fe4cbc9ba4007c36996, type: 2}
  m_ActiveProfileId: 7805f1f1ad5511943a2504643c963ddf
