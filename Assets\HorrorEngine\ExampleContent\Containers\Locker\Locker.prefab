%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1001 &4432704395513855296
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 4624752329295139123}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.28183588
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.28183588
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.28183588
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.0000002533197
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalRotation.y
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -180
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7597451064494546108, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 55b1ef6e7316bcc43a6f32586740613d, type: 2}
    - target: {fileID: -6657587173315075226, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483647
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_Name
      value: Locker
      objectReference: {fileID: 0}
    - target: {fileID: 1204907568277419843, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 55b1ef6e7316bcc43a6f32586740613d, type: 2}
    - target: {fileID: 4484923436042210290, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4484923436042210290, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4484923436042210290, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4484923436042210290, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      insertIndex: -1
      addedObject: {fileID: 617477122}
    - targetCorrespondingSourceObject: {fileID: -6657587173315075226, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2875627660371514626}
  m_SourcePrefab: {fileID: 100100000, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
--- !u!1 &2170557538547916326 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -6657587173315075226, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
  m_PrefabInstance: {fileID: 4432704395513855296}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &2875627660371514626
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2170557538547916326}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 2.0725484, y: 8.072065, z: 2.101109}
  m_Center: {x: -0.0000038146973, y: 4.0361376, z: -0.0000076293945}
--- !u!1 &3550324113067687953 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
  m_PrefabInstance: {fileID: 4432704395513855296}
  m_PrefabAsset: {fileID: 0}
--- !u!95 &617477122
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3550324113067687953}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 5f7a4e4a6656d9a4b9bc7bf01e9ac279, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 2
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!4 &4183448594551347883 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: cf4a4885148ed2449b94123e5489fece, type: 3}
  m_PrefabInstance: {fileID: 4432704395513855296}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4432704396635157599
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 664941564, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Id
      value: 63218f27-2b19-4c05-b8d6-6679d7a2932c
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_OpenClip
      value: 
      objectReference: {fileID: 8300000, guid: 0c9b2d8c4f8637d488200a3b2a5f931f, type: 3}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_CloseClip
      value: 
      objectReference: {fileID: 8300000, guid: d1ad988b6b1404745b9cf16c65acec84, type: 3}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Data.Name
      value: LOCKER
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Data.Items.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Data.Label.IsLocalized
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Data.Label.Unlocalized
      value: LOCKER
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Data.Items.Array.data[0].Item
      value: 
      objectReference: {fileID: 11400000, guid: a901d2e6294c1574dbccbee49b37812a, type: 2}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Data.Items.Array.data[1].Item
      value: 
      objectReference: {fileID: 11400000, guid: a15ea9176b3911444be55a8e5ef30927, type: 2}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Data.Items.Array.data[0].Count
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Data.Items.Array.data[1].Count
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Data.Label.Localized.m_TableEntryReference.m_KeyId
      value: 1051620635054080
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 617477122}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_Target
      value: 
      objectReference: {fileID: 617477122}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetTrigger
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_MethodName
      value: SetTrigger
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Data.Label.Localized.m_TableReference.m_TableCollectionName
      value: GUID:7bb2b06d71f243b428e4ad9bf566ec1f
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: UnityEngine.Animator, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_TargetAssemblyTypeName
      value: UnityEngine.Animator, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: Open
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_StringArgument
      value: Close
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnOpen.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4822194648758902211, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: OnClose.m_PersistentCalls.m_Calls.Array.data[0].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4985816330994596000, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Size.x
      value: 0.75
      objectReference: {fileID: 0}
    - target: {fileID: 4985816330994596000, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Size.y
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4985816330994596000, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Size.z
      value: 0.75
      objectReference: {fileID: 0}
    - target: {fileID: 8933804474734431250, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.219
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9055132001808164206, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      propertyPath: m_Name
      value: Locker
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4183448594551347883}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
--- !u!4 &4624752329295139123 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 9055132001808164204, guid: 69072a8cd8bc11148a5ffa8588ac6406, type: 3}
  m_PrefabInstance: {fileID: 4432704396635157599}
  m_PrefabAsset: {fileID: 0}
