%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: PlayerInputListenerOld
  m_TargetType:
    m_NativeTypeID: 114
    m_ManagedTypePPtr: {fileID: 11500000, guid: bab541d22d0a65448be2a57c7d51f500, type: 3}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Enabled
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorHideFlags
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EditorClassIdentifier
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_XPrimaryAxis
    value: Horizontal
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_YPrimaryAxis
    value: Vertical
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Aiming
    value: Aiming
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Attack
    value: Fire1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Interact
    value: Interact
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Run
    value: Run
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Reload
    value: Reload
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_Turn180
    value: Turn180
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
