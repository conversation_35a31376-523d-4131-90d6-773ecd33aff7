m_ProjectFiles:
  m_ManifestFileStatus:
    m_FilePath: C:/Projects/Unity/ProjectCourt/Packages/manifest.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638900229192360180
    m_Hash: 769095874
  m_LockFileStatus:
    m_FilePath: C:/Projects/Unity/ProjectCourt/Packages/packages-lock.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638900228760924029
    m_Hash: 2264124171
m_EmbeddedPackageManifests:
  m_ManifestsStatus: {}
m_LocalPackages:
  m_LocalFileStatus: []
m_ProjectPath: C:/Projects/Unity/ProjectCourt/Packages
m_EditorVersion: 6000.1.10f1 (3c681a6c22ff)
m_ResolvedPackages:
- packageId: com.unity.2d.sprite@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.2d.sprite@5eb30061d420
  assetPath: Packages/com.unity.2d.sprite
  name: com.unity.2d.sprite
  displayName: 2D Sprite
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: Use Unity Sprite Editor Window to create and edit Sprite asset properties
    like pivot, borders and Physics shape
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - 2d
  - sprite
  - sprite editor window
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 5eb30061d42006925d91d40a6a8fca10bd64bbee
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2019.2.0a1
- packageId: com.unity.addressables@2.6.0
  testable: 0
  isDirectDependency: 1
  version: 2.6.0
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.addressables@0960051b8c8a
  assetPath: Packages/com.unity.addressables
  name: com.unity.addressables
  displayName: Addressables
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'The Addressable Asset System allows the developer to ask for an asset
    via its address. Once an asset (e.g. a prefab) is marked "addressable", it generates
    an address which can be called from anywhere. Wherever the asset resides (local
    or remote), the system will locate it and its dependencies, then return it.


    Use
    ''Window->Asset Management->Addressables'' to begin working with the system.


    Addressables
    use asynchronous loading to support loading from any location with any collection
    of dependencies. Whether you have been using direct references, traditional asset
    bundles, or Resource folders, addressables provide a simpler way to make your
    game more dynamic. Addressables simultaneously opens up the world of asset bundles
    while managing all the complexity.


    For usage samples, see github.com/Unity-Technologies/Addressables-Sample'
  errors: []
  versions:
    all:
    - 0.0.8-preview
    - 0.0.12-preview
    - 0.0.15-preview
    - 0.0.16-preview
    - 0.0.18-preview
    - 0.0.22-preview
    - 0.0.26-preview
    - 0.0.27-preview
    - 0.1.2-preview
    - 0.2.1-preview
    - 0.2.2-preview
    - 0.3.5-preview
    - 0.4.6-preview
    - 0.4.8-preview
    - 0.5.2-preview
    - 0.5.3-preview
    - 0.6.6-preview
    - 0.6.7-preview
    - 0.6.8-preview
    - 0.7.4-preview
    - 0.7.5-preview
    - 0.8.4-preview
    - 0.8.6-preview
    - 1.1.3-preview
    - 1.1.4-preview
    - 1.1.5
    - 1.1.7
    - 1.1.9
    - 1.1.10
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.3.3
    - 1.3.8
    - 1.4.0
    - 1.5.0
    - 1.5.1
    - 1.6.0
    - 1.6.2
    - 1.7.4
    - 1.7.5
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.9.2
    - 1.10.0
    - 1.11.2
    - 1.12.0
    - 1.13.1
    - 1.14.2
    - 1.15.1
    - 1.16.1
    - 1.16.6
    - 1.16.7
    - 1.16.8
    - 1.16.10
    - 1.16.12
    - 1.16.13
    - 1.16.15
    - 1.16.16
    - 1.16.19
    - 1.17.0-preview
    - 1.17.2-preview
    - 1.17.4-preview
    - 1.17.5-preview
    - 1.17.6-preview
    - 1.17.13
    - 1.17.15
    - 1.17.17
    - 1.18.2
    - 1.18.4
    - 1.18.9
    - 1.18.11
    - 1.18.13
    - 1.18.15
    - 1.18.16
    - 1.18.19
    - 1.19.4
    - 1.19.6
    - 1.19.9
    - 1.19.11
    - 1.19.13
    - 1.19.14
    - 1.19.15
    - 1.19.17
    - 1.19.18
    - 1.19.19
    - 1.20.0
    - 1.20.3
    - 1.20.5
    - 1.21.1
    - 1.21.2
    - 1.21.3
    - 1.21.8
    - 1.21.9
    - 1.21.10
    - 1.21.12
    - 1.21.14
    - 1.21.15
    - 1.21.17
    - 1.21.18
    - 1.21.19
    - 1.21.20
    - 1.21.21
    - 1.22.2
    - 1.22.3
    - 1.23.1
    - 1.24.0
    - 1.25.0
    - 1.25.1
    - 2.0.3
    - 2.0.4
    - 2.0.6
    - 2.0.8
    - 2.1.0
    - 2.2.2
    - 2.3.0
    - 2.3.1
    - 2.3.7
    - 2.3.16
    - 2.4.1
    - 2.4.2
    - 2.4.3
    - 2.4.4
    - 2.4.5
    - 2.4.6
    - 2.5.0
    - 2.6.0
    - 2.7.0
    compatible:
    - 2.6.0
    - 2.7.0
    recommended: 2.6.0
    deprecated: []
  dependencies:
  - name: com.unity.profiling.core
    version: 1.0.2
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.scriptablebuildpipeline
    version: 2.4.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.profiling.core
    version: 1.0.2
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.scriptablebuildpipeline
    version: 2.4.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  keywords:
  - asset
  - resources
  - bundle
  - bundles
  - assetbundles
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638846564470030000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.addressables@2.6/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/Addressables.git
    revision: 609cc5121010016a95fcb575c4062072c4674bba
    path: 
  unityLifecycle:
    version: 2.6.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixed issue where call to LoadAssetsAsync was treating
    single element keys into an array of characters.\n- Fixed binary catalog extraction
    & optimization tools.  InternalIds of locations were getting resolved with Editor
    data incorrectly.  They are now copied directly to the output files."}'
  assetStore:
    productId: 
  fingerprint: 0960051b8c8afda16fa9fcc10021617c6d1c2286
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.6.0
    minimumUnityVersion: 2023.1.0a1
- packageId: com.unity.ai.navigation@2.0.8
  testable: 0
  isDirectDependency: 1
  version: 2.0.8
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.ai.navigation@eb5635ad590d
  assetPath: Packages/com.unity.ai.navigation
  name: com.unity.ai.navigation
  displayName: AI Navigation
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The AI Navigation package contains high-level components that allow
    you to use navmeshes to incorporate navigation and pathfinding in your game.
    With this package installed you can build and use navmeshes at runtime and at
    edit time, create dynamic obstacles, and use links to allow specific actions
    (like jumping) as your characters navigate between navmeshes.
  errors: []
  versions:
    all:
    - 1.0.0-exp.2
    - 1.0.0-exp.3
    - 1.0.0-exp.4
    - 1.1.0-pre.1
    - 1.1.0-pre.2
    - 1.1.1
    - 1.1.3
    - 1.1.4
    - 1.1.5
    - 1.1.6
    - 1.1.7
    - 2.0.0-pre.3
    - 2.0.0-pre.4
    - 2.0.0
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.6
    - 2.0.7
    - 2.0.8
    compatible:
    - 2.0.8
    recommended: 2.0.8
    deprecated: []
  dependencies:
  - name: com.unity.modules.ai
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ai
    version: 1.0.0
  keywords:
  - carving
  - mesh
  - navigation
  - navmesh
  - NavMesh
  - navmesh agent
  - navmesh link
  - navmeshlink
  - navmesh modifier
  - navmeshmodifier
  - navmesh modifier volume
  - navmeshmodifiervolume
  - navmesh obstacle
  - navmesh surface
  - navmeshsurface
  - offmesh link
  - off-mesh link
  - pathfinding
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638841129733020000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ai.navigation@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ai.navigation.git
    revision: edeb8e889d1eedf4001fa41bdab7d69d01a62f24
    path: 
  unityLifecycle:
    version: 2.0.8
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n* Samples will now use appropriate material
    colors in projects using HDRP or URP. Install the **com.unity.shadergraph** package
    to show the materials correctly for built-in render pipeline projects."}'
  assetStore:
    productId: 
  fingerprint: eb5635ad590d47cef2a5c920d9475cc222db3f67
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.8
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.cinemachine@3.1.3
  testable: 0
  isDirectDependency: 1
  version: 3.1.3
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.cinemachine@624f919d8cdd
  assetPath: Packages/com.unity.cinemachine
  name: com.unity.cinemachine
  displayName: Cinemachine
  author:
    name: 
    email: 
    url: 
  category: cinematography
  type: 
  description: "Smart camera tools for passionate creators. \n\nCinemachine 3 is
    a newer and better version of Cinemachine, but upgrading an existing project
    from 2.X will likely require some effort.  If you're considering upgrading an
    older project, please see our upgrade guide in the user manual."
  errors: []
  versions:
    all:
    - 2.1.11-beta.1
    - 2.1.12
    - 2.1.13
    - 2.2.0
    - 2.2.7
    - 2.2.8
    - 2.2.9
    - 2.2.10-preview.3
    - 2.2.10-preview.4
    - 2.3.1
    - 2.3.3
    - 2.3.4
    - 2.3.5-preview.3
    - 2.4.0-preview.3
    - 2.4.0-preview.4
    - 2.4.0-preview.6
    - 2.4.0-preview.7
    - 2.4.0-preview.8
    - 2.4.0-preview.9
    - 2.4.0-preview.10
    - 2.4.0
    - 2.5.0
    - 2.6.0-preview.2
    - 2.6.0-preview.3
    - 2.6.0-preview.5
    - 2.6.0-preview.8
    - 2.6.0
    - 2.6.1-preview.6
    - 2.6.1
    - 2.6.2-preview.1
    - 2.6.2
    - 2.6.3-preview.2
    - 2.6.3
    - 2.6.4
    - 2.6.5
    - 2.6.9
    - 2.6.10
    - 2.6.11
    - 2.6.14
    - 2.6.15
    - 2.6.17
    - 2.7.1
    - 2.7.2
    - 2.7.3
    - 2.7.4
    - 2.7.5
    - 2.7.8
    - 2.7.9
    - 2.8.0-exp.1
    - 2.8.0-exp.2
    - 2.8.0-pre.1
    - 2.8.0
    - 2.8.1
    - 2.8.2
    - 2.8.3
    - 2.8.4
    - 2.8.6
    - 2.8.9
    - 2.9.0-pre.1
    - 2.9.0-pre.6
    - 2.9.1
    - 2.9.2
    - 2.9.4
    - 2.9.5
    - 2.9.7
    - 2.10.0
    - 2.10.1
    - 2.10.2
    - 2.10.3
    - 2.10.4
    - 3.0.0-pre.3
    - 3.0.0-pre.4
    - 3.0.0-pre.5
    - 3.0.0-pre.6
    - 3.0.0-pre.7
    - 3.0.0-pre.8
    - 3.0.0-pre.9
    - 3.0.1
    - 3.1.0
    - 3.1.1
    - 3.1.2
    - 3.1.3
    - 3.1.4
    compatible:
    - 2.10.4
    - 3.0.0-pre.3
    - 3.0.0-pre.4
    - 3.0.0-pre.5
    - 3.0.0-pre.6
    - 3.0.0-pre.7
    - 3.0.0-pre.8
    - 3.0.0-pre.9
    - 3.0.1
    - 3.1.0
    - 3.1.1
    - 3.1.2
    - 3.1.3
    - 3.1.4
    recommended: 3.1.4
    deprecated: []
  dependencies:
  - name: com.unity.splines
    version: 2.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.splines
    version: 2.8.1
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords:
  - camera
  - follow
  - rig
  - fps
  - cinematography
  - aim
  - orbit
  - cutscene
  - cinematic
  - collision
  - freelook
  - cinemachine
  - compose
  - composition
  - dolly
  - track
  - clearshot
  - noise
  - framing
  - handheld
  - lens
  - impulse
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638739355014480000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.cinemachine@3.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.cinemachine.git
    revision: 23940e5601df6628d349dc9d1912bd2bb34ad631
    path: 
  unityLifecycle:
    version: 2.10.4
    nextVersion: 
    recommendedVersion: 3.1.0
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Bugfixes\n- Regression fix: CinemachinePanTilt
    recentering was ignoring axis Center setting.\n- CameraDeactivated events were
    not sent consistently when a blend interrupted another blend before completion.\n-
    CameraActivated events were not sent consistently when activation was due to
    timeline blends.\n- FramingTransposer with a dead zone would sometimes drift.\n-
    Decollider would sometimes cause camera to slip inside cracks between adjacent
    colliders.\n- The Deoccluder failed to reset its state when initially enabled,
    and sometimes caused small spurious camera rotations.\n- Fixed the Radial Axis
    input axis in the CinemachineOrbitalFollow component to map to the y axis.\n-
    Desired blend time is respected when interrupting blends with heterogeneous blend-in
    and blend-out times.\n\n### Changed\n- Added delayed processing to near and far
    clip plane inspector fields for the CinemachineCamera lens.\n- Updated the gamepad
    inputs in the CinemachineDefaultInputActions asset to closer match standard gamepad
    conventions. Renamed the Player action map to CM Default.\n\n### Added\n- CinemachineConfiner2D.BoundingShapeIsBaked
    can be used to check if the confiner''s bounding shape is baked.\n- CinemachineConfiner2D.BakeBoundingShape()
    can be used to force-complete the confiner''s baking of the bounding shape."}'
  assetStore:
    productId: 
  fingerprint: 624f919d8cdd376712eda21b43abcb0330c023e2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.10.4
    minimumUnityVersion: 2022.3.0a1
- packageId: com.unity.collab-proxy@2.8.2
  testable: 0
  isDirectDependency: 1
  version: 2.8.2
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f
  assetPath: Packages/com.unity.collab-proxy
  name: com.unity.collab-proxy
  displayName: Version Control
  author:
    name: 
    email: 
    url: 
  category: Editor
  type: 
  description: The package gives you the ability to use Unity Version Control in
    the Unity editor. To use Unity Version Control, a subscription is required. Learn
    more about how you can get started for free by visiting https://unity.com/solutions/version-control
  errors: []
  versions:
    all:
    - 1.2.3-preview
    - 1.2.4-preview
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.11
    - 1.2.15
    - 1.2.16
    - 1.2.17-preview.3
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.9
    - 1.5.7
    - 1.6.0
    - 1.7.1
    - 1.8.0
    - 1.9.0
    - 1.10.2
    - 1.11.2
    - 1.12.5
    - 1.13.5
    - 1.14.1
    - 1.14.4
    - 1.14.7
    - 1.14.9
    - 1.14.12
    - 1.14.13
    - 1.14.15
    - 1.14.16
    - 1.14.17
    - 1.14.18
    - 1.15.1
    - 1.15.4
    - 1.15.7
    - 1.15.9
    - 1.15.12
    - 1.15.13
    - 1.15.15
    - 1.15.16
    - 1.15.17
    - 1.15.18
    - 1.17.0
    - 1.17.1
    - 1.17.2
    - 1.17.6
    - 1.17.7
    - 2.0.0-preview.6
    - 2.0.0-preview.8
    - 2.0.0-preview.15
    - 2.0.0-preview.17
    - 2.0.0-preview.20
    - 2.0.0-preview.21
    - 2.0.0-preview.22
    - 2.0.0
    - 2.0.1
    - 2.0.3
    - 2.0.4
    - 2.0.5
    - 2.0.7
    - 2.1.0-preview.3
    - 2.1.0-preview.5
    - 2.1.0-preview.6
    - 2.1.0
    - 2.2.0
    - 2.3.1
    - 2.4.3
    - 2.4.4
    - 2.5.1
    - 2.5.2
    - 2.6.0
    - 2.7.1
    - 2.8.1
    - 2.8.2
    compatible:
    - 2.8.2
    recommended: 2.8.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - backup
  - cloud
  - collab
  - collaborate
  - collaboration
  - control
  - devops
  - plastic
  - plasticscm
  - source
  - team
  - teams
  - version
  - vcs
  - uvcs
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638822079721750000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collab-proxy@2.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.cloud.collaborate.git
    revision: c1fbc35e6bbae1ac0e9a3481e441dfdb3602b5ef
    path: 
  unityLifecycle:
    version: 2.8.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixed false positive error showing in console if
    creating a workspace from the Hub with a version of the Unity Editor shipping
    with a default Version Control package older than version 2.7.1."}'
  assetStore:
    productId: 
  fingerprint: c854d1f7d97fbe1905f3e3591ded6fe77d96e654
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.8.2
    minimumUnityVersion: 2021.3.0f1
- packageId: com.unity.ide.rider@3.0.36
  testable: 0
  isDirectDependency: 1
  version: 3.0.36
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.ide.rider@4d374c7eb6db
  assetPath: Packages/com.unity.ide.rider
  name: com.unity.ide.rider
  displayName: JetBrains Rider Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The JetBrains Rider Editor package provides an integration for using
    the JetBrains Rider IDE as a code editor for Unity. It adds support for generating
    .csproj files for code completion and auto-discovery of installations.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.6
    - 1.0.8
    - 1.1.0
    - 1.1.1
    - 1.1.2-preview
    - 1.1.2-preview.2
    - 1.1.3-preview.1
    - 1.1.4-preview
    - 1.1.4
    - 1.2.0-preview
    - 1.2.1
    - 2.0.0-preview
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.9
    - 3.0.10
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 3.0.20
    - 3.0.21
    - 3.0.22
    - 3.0.24
    - 3.0.25
    - 3.0.26
    - 3.0.27
    - 3.0.28
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    compatible:
    - 3.0.36
    recommended: 3.0.36
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638799562419060000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.rider@3.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.rider.git
    revision: b5315083ab3861d21f6ab2ed0d9514daf04bf208
    path: 
  unityLifecycle:
    version: 3.0.36
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- fix RIDER-124592 Avoid affecting \"Strip Engine Code\"
    while IL2CPP debug enabled"}'
  assetStore:
    productId: 
  fingerprint: 4d374c7eb6db6907c7e6925e3086c3c73f926e13
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.0.36
    minimumUnityVersion: 2019.4.6f1
- packageId: com.unity.ide.visualstudio@2.0.23
  testable: 0
  isDirectDependency: 1
  version: 2.0.23
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.ide.visualstudio@198cdf337d13
  assetPath: Packages/com.unity.ide.visualstudio
  name: com.unity.ide.visualstudio
  displayName: Visual Studio Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Code editor integration for supporting Visual Studio as code editor
    for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.9
    - 1.0.10
    - 1.0.11
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 2.0.8
    - 2.0.9
    - 2.0.11
    - 2.0.12
    - 2.0.14
    - 2.0.15
    - 2.0.16
    - 2.0.17
    - 2.0.18
    - 2.0.20
    - 2.0.21
    - 2.0.22
    - 2.0.23
    compatible:
    - 2.0.23
    recommended: 2.0.23
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.9
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638786696173840000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.visualstudio@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.visualstudio.git
    revision: 0fe3b29f9aff2b90b9f0962ae35036a824d3dd6b
    path: 
  unityLifecycle:
    version: 2.0.23
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"Integration:\n\n- Monitor `additionalfile` extension
    by default.\n- Try opening a Visual Studio Code workspace if there''s one (`.code-workspace`
    file in the Unity project).\n\nProject generation:\n\n- Identify `asset`, `meta`,
    `prefab` and `unity` files as `yaml` (Visual Studio Code).\n- Add `sln`/`csproj`
    file nesting (Visual Studio Code).\n- Improve SDK style project generation."}'
  assetStore:
    productId: 
  fingerprint: 198cdf337d13c83ca953581515630d66b779e92b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.23
    minimumUnityVersion: 2019.4.25f1
- packageId: com.unity.inputsystem@1.14.0
  testable: 0
  isDirectDependency: 1
  version: 1.14.0
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.inputsystem@7fe8299111a7
  assetPath: Packages/com.unity.inputsystem
  name: com.unity.inputsystem
  displayName: Input System
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A new input system which can be used as a more extensible and customizable
    alternative to Unity's classic input system in UnityEngine.Input.
  errors: []
  versions:
    all:
    - 0.1.2-preview
    - 0.2.0-preview
    - 0.2.1-preview
    - 0.2.6-preview
    - 0.2.8-preview
    - 0.2.10-preview
    - 0.9.0-preview
    - 0.9.1-preview
    - 0.9.2-preview
    - 0.9.3-preview
    - 0.9.4-preview
    - 0.9.5-preview
    - 0.9.6-preview
    - 1.0.0-preview
    - 1.0.0-preview.1
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.0.0-preview.5
    - 1.0.0-preview.6
    - 1.0.0-preview.7
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.1.0-pre.5
    - 1.1.0-pre.6
    - 1.1.0-preview.1
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.1
    - 1.2.0
    - 1.3.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.5.0
    - 1.5.1
    - 1.6.1
    - 1.6.3
    - 1.7.0
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.9.0
    - 1.10.0
    - 1.11.0
    - 1.11.1
    - 1.11.2
    - 1.12.0
    - 1.13.0
    - 1.13.1
    - 1.14.0
    - 1.14.1
    compatible:
    - 1.14.0
    - 1.14.1
    recommended: 1.14.1
    deprecated: []
  dependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - input
  - events
  - keyboard
  - mouse
  - gamepad
  - touch
  - vr
  - xr
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638780752796560000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.inputsystem@1.14/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/InputSystem.git
    revision: 5ac6cb14e03a4e4e5faf7bd8fbd915105ae3ff55
    path: 
  unityLifecycle:
    version: 1.14.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed an issue where all action maps were
    enabled initially for project wide actions, which overrode the PlayerInput action
    map configuration. [ISXB-920](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-920)\n-
    Fixed an issue where ButtonStates are not fully updated when switching SingleUnifiedPointer.
    [ISXB-1356](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1356)\n-
    Fixed errors when pasting composite parts into non-composites. [ISXB-757](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-757)\n\n###
    Changed\n- Changed enum value `Key.IMESelected` to obsolete which was not a real
    key. Please use the ButtonControl `imeSelected`.\n\n### Added\n- Added support
    of F13-F24 keys. [UUM-44328](https://issuetracker.unity3d.com/product/unity/issues/guid/UUM-44328)"}'
  assetStore:
    productId: 
  fingerprint: 7fe8299111a78212d8968229ab41a82e4991ba25
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.14.0
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.localization@1.5.4
  testable: 0
  isDirectDependency: 1
  version: 1.5.4
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.localization@f2647f7408bd
  assetPath: Packages/com.unity.localization
  name: com.unity.localization
  displayName: Localization
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "Use the Localization package to easily configure localization settings
    for your application.\n\nAdd support for multiple languages and regional variants,
    including:\n\n\u2022 String localization: Set different strings to display based
    on locale. Use the Smart Strings feature to add logic to automatically replace
    specific strings, such as placeholders and plurals.\n\u2022 Asset localization:
    Use a different asset (such as a texture, model, or audio file) based on a locale.\n\u2022
    Pseudo-localization: Test how your project will adapt to different localizations
    at an early stage, before adding your translations.\n\u2022 Import and export
    localization data to XLIFF, CSV and Google Sheets.\n\nAdd localization to your
    projects using the Localization package to help make your applications more accessible
    to a wider audience."
  errors: []
  versions:
    all:
    - 0.2.1-preview
    - 0.2.2-preview
    - 0.2.3-preview
    - 0.3.1-preview
    - 0.3.2-preview
    - 0.4.0-preview
    - 0.5.0-preview
    - 0.5.1-preview
    - 0.6.0-preview
    - 0.6.1-preview
    - 0.7.1-preview
    - 0.8.0-preview
    - 0.8.1-preview
    - 0.9.0-preview
    - 0.10.0-preview
    - 0.11.0-preview
    - 0.11.1-preview
    - 1.0.0-pre.8
    - 1.0.0-pre.9
    - 1.0.0-pre.10
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.5
    - 1.1.0
    - 1.1.1
    - 1.2.1
    - 1.3.1
    - 1.3.2
    - 1.4.0-exp.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.5.0-pre.2
    - 1.5.0-pre.3
    - 1.5.0-pre.4
    - 1.5.0-pre.5
    - 1.5.0-pre.6
    - 1.5.0-pre.7
    - 1.5.1
    - 1.5.2
    - 1.5.3
    - 1.5.4
    - 1.5.5
    compatible:
    - 1.5.4
    - 1.5.5
    recommended: 1.5.5
    deprecated: []
  dependencies:
  - name: com.unity.addressables
    version: 1.22.2
  - name: com.unity.nuget.newtonsoft-json
    version: 3.0.2
  resolvedDependencies:
  - name: com.unity.addressables
    version: 2.6.0
  - name: com.unity.profiling.core
    version: 1.0.2
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.scriptablebuildpipeline
    version: 2.4.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  keywords:
  - localization
  - locale
  - language
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638723845131630000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.localization@1.5/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.localization.git
    revision: 5e39cb8be2051821e233973d43c66ee03ea7668e
    path: 
  unityLifecycle:
    version: 1.5.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n\n- Fixed an issue where we would remove
    an asset from Addressables when it was still being used by another entry. ([LOC-1159](https://issuetracker.unity3d.com/product/unity/issues/guid/LOC-1159))\n-
    Fixed localized property variant changes not being applied correctly when updating
    the values via the GameObjectLocalizer inspector. ([LOC-1169](https://issuetracker.unity3d.com/product/unity/issues/guid/LOC-1169))\n-
    Fixed LocalizedAsset property drawer not handling Lists of LocalizedAssets. ([LOC-1182](https://issuetracker.unity3d.com/product/unity/issues/guid/LOC-1182))\n-
    Fixed possible NullReferenceException due to disposed of SerializedProperties
    when selecting items in the localized reference picker. ([LOC-1179](https://issuetracker.unity3d.com/product/unity/issues/guid/LOC-1179))\n-
    Fixed the Conditional formatter so that it uses the invariant culture when parsing
    decimal values. ([LOC-1176](https://issuetracker.unity3d.com/product/unity/issues/guid/LOC-1176))\n-
    Fixed the LocalizedReference dropdown label not updating when the key was renamed.
    ([LOC-1165](https://issuetracker.unity3d.com/product/unity/issues/guid/LOC-1166))\n-
    Fixed the LocalizedString property drawer so it does not display \"Entry Name\"
    in the corner. ([LOC-1165](https://issuetracker.unity3d.com/product/unity/issues/guid/LOC-1165))"}'
  assetStore:
    productId: 
  fingerprint: f2647f7408bdc5be4c549b55b11bffcdcbb91966
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.5.4
    minimumUnityVersion: 2019.4.0a1
- packageId: com.unity.multiplayer.center@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.multiplayer.center@f3fb577b3546
  assetPath: Packages/com.unity.multiplayer.center
  name: com.unity.multiplayer.center
  displayName: Multiplayer Center
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The multiplayer center provides a starting point to create multiplayer
    games. It will recommend specific packages and enable you to easily access integrations,
    samples and documentation.
  errors: []
  versions:
    all:
    - 0.2.1
    - 0.3.0
    - 0.4.0
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - Multiplayer
  - Netcode
  - Services
  - Tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: f3fb577b3546594b97b8cc34307cd621f60f1c73
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.render-pipelines.universal@17.1.0
  testable: 0
  isDirectDependency: 1
  version: 17.1.0
  source: 2
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.render-pipelines.universal@150a31bd1157
  assetPath: Packages/com.unity.render-pipelines.universal
  name: com.unity.render-pipelines.universal
  displayName: Universal Render Pipeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Universal Render Pipeline (URP) is a prebuilt Scriptable Render
    Pipeline, made by Unity. URP provides artist-friendly workflows that let you
    quickly and easily create optimized graphics across a range of platforms, from
    mobile to high-end consoles and PCs.
  errors: []
  versions:
    all:
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.14
    - 9.0.0-preview.35
    - 9.0.0-preview.55
    - 9.0.0-preview.72
    - 10.0.0-preview.26
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.1.0
    compatible:
    - 17.1.0
    recommended: 17.1.0
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.shadergraph
    version: 17.1.0
  - name: com.unity.render-pipelines.universal-config
    version: 17.0.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.shadergraph
    version: 17.1.0
  - name: com.unity.searcher
    version: 4.9.3
  - name: com.unity.render-pipelines.universal-config
    version: 17.0.3
  keywords:
  - graphics
  - performance
  - rendering
  - mobile
  - render
  - pipeline
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 150a31bd115792030862dad6f36d161bfe891c8d
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.1.0
    minimumUnityVersion: 6000.1.0a1
- packageId: com.unity.test-framework@1.5.1
  testable: 0
  isDirectDependency: 1
  version: 1.5.1
  source: 2
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.test-framework@7056e7f856e9
  assetPath: Packages/com.unity.test-framework
  name: com.unity.test-framework
  displayName: Test Framework
  author:
    name: 
    email: 
    url: 
  category: Unity Test Framework
  type: 
  description: Test framework for running Edit mode and Play mode tests in Unity.
  errors: []
  versions:
    all:
    - 0.0.4-preview
    - 0.0.29-preview
    - 1.0.0
    - 1.0.7
    - 1.0.9
    - 1.0.11
    - 1.0.12
    - 1.0.13
    - 1.0.14
    - 1.0.16
    - 1.0.17
    - 1.0.18
    - 1.1.0
    - 1.1.1
    - 1.1.2
    - 1.1.3
    - 1.1.5
    - 1.1.8
    - 1.1.9
    - 1.1.11
    - 1.1.13
    - 1.1.14
    - 1.1.16
    - 1.1.18
    - 1.1.19
    - 1.1.20
    - 1.1.22
    - 1.1.24
    - 1.1.26
    - 1.1.27
    - 1.1.29
    - 1.1.30
    - 1.1.31
    - 1.1.33
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.5.1
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    compatible:
    - 1.5.1
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    recommended: 1.5.1
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 2.0.3
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - Test
  - TestFramework
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.5.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 7056e7f856e91f1d294fbe8db0c8fea1ffd9ad5d
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.5.1
    minimumUnityVersion: 2022.3.0a1
- packageId: com.unity.timeline@1.8.8
  testable: 0
  isDirectDependency: 1
  version: 1.8.8
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.timeline@fa3a0bab2b90
  assetPath: Packages/com.unity.timeline
  name: com.unity.timeline
  displayName: Timeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Use Unity Timeline to create cinematic content, game-play sequences,
    audio sequences, and complex particle effects.
  errors: []
  versions:
    all:
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.10
    - 1.2.11
    - 1.2.12
    - 1.2.13
    - 1.2.14
    - 1.2.15
    - 1.2.16
    - 1.2.17
    - 1.2.18
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.5
    - 1.4.0-preview.6
    - 1.4.0-preview.7
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.5.0-pre.2
    - 1.5.0-preview.1
    - 1.5.0-preview.2
    - 1.5.0-preview.3
    - 1.5.0-preview.4
    - 1.5.0-preview.5
    - 1.5.1-pre.1
    - 1.5.1-pre.2
    - 1.5.1-pre.3
    - 1.5.2
    - 1.5.4
    - 1.5.5
    - 1.5.6
    - 1.5.7
    - 1.6.0-pre.1
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0-pre.5
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.7.0-pre.1
    - 1.7.0-pre.2
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.8.6
    - 1.8.7
    - 1.8.8
    - 1.8.9
    compatible:
    - 1.8.7
    - 1.8.8
    - 1.8.9
    recommended: 1.8.9
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  keywords:
  - unity
  - animation
  - editor
  - timeline
  - tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638739338167810000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.timeline@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git
    revision: b01f8fc6766a468476830dec57fdf694f16104e6
    path: 
  unityLifecycle:
    version: 1.8.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n\n- Fixed bug where the first property,
    when it is a collection of objects, of a TrackAsset would not be properly displayed
    in the inspector.\n- TimelineAsset.EditorSettings.SetStandardFrameRate would
    incorrectly throw an ArgumentException if given a valid StandardFrameRates, this
    has been corrected.\n- Clip blends will now be computed when using the API to
    add clips to tracks - IN-66759\n- Improved performance when evaluating Timeline
    Playables with a large number of clip based Animation Tracks (TB-259)\n- Updated
    the Gameplay Sequence sample to show materials when using the Universal and HD
    Render Pipelines."}'
  assetStore:
    productId: 
  fingerprint: fa3a0bab2b909389f8e6c20d5ff275c9e15ae0a2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.7
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.ugui@2.0.0
  testable: 0
  isDirectDependency: 1
  version: 2.0.0
  source: 2
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.ugui@57cef44123c7
  assetPath: Packages/com.unity.ugui
  name: com.unity.ugui
  displayName: Unity UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "Unity UI is a set of tools for developing user interfaces for games
    and applications. It is a GameObject-based UI system that uses Components and
    the Game View to arrange, position, and style user interfaces. \u200B You cannot
    use Unity UI to create or change user interfaces in the Unity Editor."
  errors: []
  versions:
    all:
    - 2.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    compatible:
    - 2.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    recommended: 2.0.0
    deprecated:
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - UI
  - ugui
  - Unity UI
  - Canvas
  - TextMeshPro
  - TextMesh Pro
  - Text
  - TMP
  - SDF
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 57cef44123c7486b2e2f08dc6535aecf6c34b8ef
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.0
    minimumUnityVersion: 2019.2.0a1
- packageId: com.unity.visualscripting@1.9.7
  testable: 0
  isDirectDependency: 1
  version: 1.9.7
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.visualscripting@6279e2b7c485
  assetPath: Packages/com.unity.visualscripting
  name: com.unity.visualscripting
  displayName: Visual Scripting
  author:
    name: 
    email: 
    url: 
  category: 
  type: tool
  description: 'Visual scripting is a workflow that uses visual, node-based graphs
    to design behaviors rather than write lines of C# script.


    Enabling artists,
    designers and programmers alike, visual scripting can be used to design final
    logic, quickly create prototypes, iterate on gameplay and create custom nodes
    to help streamline collaboration.


    Visual scripting is compatible with third-party
    APIs, including most packages, assets and custom libraries.'
  errors: []
  versions:
    all:
    - 1.5.0
    - 1.5.1-pre.3
    - 1.5.1-pre.5
    - 1.5.1
    - 1.5.2
    - 1.6.0-pre.3
    - 1.6.0
    - 1.6.1
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.7.8
    - 1.8.0-pre.1
    - 1.8.0
    - 1.9.0
    - 1.9.1
    - 1.9.2
    - 1.9.4
    - 1.9.5
    - 1.9.6
    - 1.9.7
    - 1.9.8
    compatible:
    - 1.9.7
    - 1.9.8
    recommended: 1.9.8
    deprecated:
    - 1.8.0-pre.1
  dependencies:
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638835226812630000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.visualscripting.git
    revision: 8292335505915a41ec7c5cc8e08ac436e24120a9
    path: 
  unityLifecycle:
    version: 1.9.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed a warning \"Unable to load Unity.Android.Gradle''s
    referenced assembly NiceIO\" when scanning assemblies. [UVSB-2594](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2594)\n-
    Fixed error when trying to load fuzzy finder on multi screen setup on Mac. [UVSB-2419](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2419)\n-
    Fixed the `AOTSafeMode` project setting appearing in the Editor Preferences window.
    It is now shown in the Project Settings tab for Visual Scripting. [UVSB-2590](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2590)\n-
    Fixed possible crash on VisionOS. [UVSB-2565](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2565)\n\n###
    Changed\n- The `AOTSafeMode` project setting has been marked as not visible,
    it will no longer be included when calling `ConfigurationPanel.GetSearchKeywords`.
    [UVSB-2590](https://issuetracker.unity3d.com/product/unity/issues/guid/UVSB-2590)"}'
  assetStore:
    productId: 
  fingerprint: 6279e2b7c4858e56cca7f367cd38c49ef66778c9
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.9.7
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.modules.accessibility@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.accessibility
  assetPath: Packages/com.unity.modules.accessibility
  name: com.unity.modules.accessibility
  displayName: Accessibility
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Accessibility module includes utilities to facilitate the development
    of accessible user experiences in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AccessibilityModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.ai@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ai
  assetPath: Packages/com.unity.modules.ai
  name: com.unity.modules.ai
  displayName: AI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AI module implements the path finding features in Unity. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.androidjni@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.androidjni
  assetPath: Packages/com.unity.modules.androidjni
  name: com.unity.modules.androidjni
  displayName: Android JNI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'AndroidJNI module allows you to call Java code. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AndroidJNIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.animation@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.animation
  assetPath: Packages/com.unity.modules.animation
  name: com.unity.modules.animation
  displayName: Animation
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Animation module implements Unity''s animation system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AnimationModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.assetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.assetbundle
  assetPath: Packages/com.unity.modules.assetbundle
  name: com.unity.modules.assetbundle
  displayName: Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AssetBundle module implements the AssetBundle class and related
    APIs to load data from AssetBundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.audio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.audio
  assetPath: Packages/com.unity.modules.audio
  name: com.unity.modules.audio
  displayName: Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Audio module implements Unity''s audio system. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.AudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.cloth@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.cloth
  assetPath: Packages/com.unity.modules.cloth
  name: com.unity.modules.cloth
  displayName: Cloth
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Cloth module implements cloth physics simulation through the
    Cloth component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ClothModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.director@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.director
  assetPath: Packages/com.unity.modules.director
  name: com.unity.modules.director
  displayName: Director
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Director module implements the PlayableDirector class. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.DirectorModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.imageconversion@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imageconversion
  assetPath: Packages/com.unity.modules.imageconversion
  name: com.unity.modules.imageconversion
  displayName: Image Conversion
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ImageConversion module implements the ImageConversion class which
    provides helper methods for converting image data. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.imgui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imgui
  assetPath: Packages/com.unity.modules.imgui
  name: com.unity.modules.imgui
  displayName: IMGUI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The IMGUI module provides Unity''s immediate mode GUI solution for
    creating in-game and editor user interfaces. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.IMGUIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.jsonserialize@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.jsonserialize
  assetPath: Packages/com.unity.modules.jsonserialize
  name: com.unity.modules.jsonserialize
  displayName: JSONSerialize
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The JSONSerialize module provides the JsonUtility class which lets
    you serialize Unity Objects to JSON format. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.JSONSerializeModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.particlesystem@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.particlesystem
  assetPath: Packages/com.unity.modules.particlesystem
  name: com.unity.modules.particlesystem
  displayName: Particle System
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ParticleSystem module implements Unity''s Particle System. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.ParticleSystemModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.physics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics
  assetPath: Packages/com.unity.modules.physics
  name: com.unity.modules.physics
  displayName: Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics module implements 3D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.PhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.physics2d@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics2d
  assetPath: Packages/com.unity.modules.physics2d
  name: com.unity.modules.physics2d
  displayName: Physics 2D
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics2d module implements 2D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.Physics2DModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.screencapture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.screencapture
  assetPath: Packages/com.unity.modules.screencapture
  name: com.unity.modules.screencapture
  displayName: Screen Capture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ScreenCapture module provides functionality to take screen shots
    using the ScreenCapture class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ScreenCaptureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.terrain@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrain
  assetPath: Packages/com.unity.modules.terrain
  name: com.unity.modules.terrain
  displayName: Terrain
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Terrain module implements Unity''s Terrain rendering engine available
    through the Terrain component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.terrainphysics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrainphysics
  assetPath: Packages/com.unity.modules.terrainphysics
  name: com.unity.modules.terrainphysics
  displayName: Terrain Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The TerrainPhysics module connects the Terrain and Physics modules
    by implementing the TerrainCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainPhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.tilemap@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.tilemap
  assetPath: Packages/com.unity.modules.tilemap
  name: com.unity.modules.tilemap
  displayName: Tilemap
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Tilemap module implements the Tilemap class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TilemapModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.ui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ui
  assetPath: Packages/com.unity.modules.ui
  name: com.unity.modules.ui
  displayName: UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UI module implements basic components required for Unity''s UI
    system Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.uielements@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.uielements
  assetPath: Packages/com.unity.modules.uielements
  name: com.unity.modules.uielements
  displayName: UIElements
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UIElements module implements the UIElements retained mode UI
    framework. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIElementsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.umbra@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.umbra
  assetPath: Packages/com.unity.modules.umbra
  name: com.unity.modules.umbra
  displayName: Umbra
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Umbra module implements Unity''s occlusion culling system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.UmbraModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unityanalytics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unityanalytics
  assetPath: Packages/com.unity.modules.unityanalytics
  name: com.unity.modules.unityanalytics
  displayName: Unity Analytics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequest@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequest
  assetPath: Packages/com.unity.modules.unitywebrequest
  name: com.unity.modules.unitywebrequest
  displayName: Unity Web Request
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequest module lets you communicate with http services.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestassetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestassetbundle
  assetPath: Packages/com.unity.modules.unitywebrequestassetbundle
  name: com.unity.modules.unitywebrequestassetbundle
  displayName: Unity Web Request Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAssetBundle module provides the DownloadHandlerAssetBundle
    class to use UnityWebRequest to download Asset Bundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestaudio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestaudio
  assetPath: Packages/com.unity.modules.unitywebrequestaudio
  name: com.unity.modules.unitywebrequestaudio
  displayName: Unity Web Request Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAudio module provides the DownloadHandlerAudioClip
    class to use UnityWebRequest to download AudioClips. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequesttexture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequesttexture
  assetPath: Packages/com.unity.modules.unitywebrequesttexture
  name: com.unity.modules.unitywebrequesttexture
  displayName: Unity Web Request Texture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestTexture module provides the DownloadHandlerTexture
    class to use UnityWebRequest to download Textures. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestTextureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestwww@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestwww
  assetPath: Packages/com.unity.modules.unitywebrequestwww
  name: com.unity.modules.unitywebrequestwww
  displayName: Unity Web Request WWW
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestWWW module implements the legacy WWW lets you
    communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.vehicles@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vehicles
  assetPath: Packages/com.unity.modules.vehicles
  name: com.unity.modules.vehicles
  displayName: Vehicles
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Vehicles module implements vehicle physics simulation through
    the WheelCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VehiclesModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.video@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.video
  assetPath: Packages/com.unity.modules.video
  name: com.unity.modules.video
  displayName: Video
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Video module lets you play back video files in your content.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VideoModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.vr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vr
  assetPath: Packages/com.unity.modules.vr
  name: com.unity.modules.vr
  displayName: VR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The VR module implements support for virtual reality devices in Unity.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.wind@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.wind
  assetPath: Packages/com.unity.modules.wind
  name: com.unity.modules.wind
  displayName: Wind
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Wind module implements the WindZone component which can affect
    terrain rendering and particle simulations. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.WindModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.xr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.xr
  assetPath: Packages/com.unity.modules.xr
  name: com.unity.modules.xr
  displayName: XR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The XR module contains the VR and AR related platform support functionality.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.XRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.subsystems@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.subsystems
  assetPath: Packages/com.unity.modules.subsystems
  name: com.unity.modules.subsystems
  displayName: Subsystems
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Subsystem module contains the definitions and runtime support
    for general subsystems in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.SubsystemsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.hierarchycore@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.hierarchycore
  assetPath: Packages/com.unity.modules.hierarchycore
  name: com.unity.modules.hierarchycore
  displayName: Hierarchy Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'Module that contains a high-performance hierarchy container. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.HierarchyCoreModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.ext.nunit@2.0.5
  testable: 0
  isDirectDependency: 0
  version: 2.0.5
  source: 2
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.ext.nunit@031a54704bff
  assetPath: Packages/com.unity.ext.nunit
  name: com.unity.ext.nunit
  displayName: Custom NUnit
  author:
    name: 
    email: 
    url: 
  category: Libraries
  type: 
  description: A custom version of NUnit used by Unity Test Framework. Based on NUnit
    version 3.5 and works with all platforms, il2cpp and Mono AOT.
  errors: []
  versions:
    all:
    - 0.1.5-preview
    - 0.1.6-preview
    - 0.1.9-preview
    - 1.0.0
    - 1.0.5
    - 1.0.6
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    compatible:
    - 2.0.5
    recommended: 2.0.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - nunit
  - unittest
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ext.nunit@2.0/manual/index.html
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 031a54704bffe39e6a0324909f8eaa4565bdebf2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.5
    minimumUnityVersion: 2019.4.0a1
- packageId: com.unity.render-pipelines.core@17.1.0
  testable: 0
  isDirectDependency: 0
  version: 17.1.0
  source: 2
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.render-pipelines.core@cedf7ca623df
  assetPath: Packages/com.unity.render-pipelines.core
  name: com.unity.render-pipelines.core
  displayName: Scriptable Render Pipeline Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: SRP Core makes it easier to create or customize a Scriptable Render
    Pipeline (SRP). SRP Core contains reusable code, including boilerplate code for
    working with platform-specific graphics APIs, utility functions for common rendering
    operations, and  shader libraries. The code in SRP Core is use by the High Definition
    Render Pipeline (HDRP) and Universal Render Pipeline (URP). If you are creating
    a custom SRP from scratch or customizing a prebuilt SRP, using SRP Core will
    save you time.
  errors: []
  versions:
    all:
    - 0.1.21
    - 0.1.27
    - 0.1.28
    - 1.0.0-beta
    - 1.0.1-beta
    - 1.1.1-preview
    - 1.1.2-preview
    - 1.1.4-preview
    - 1.1.5-preview
    - 1.1.8-preview
    - 1.1.10-preview
    - 1.1.11-preview
    - 2.0.1-preview
    - 2.0.3-preview
    - 2.0.4-preview
    - 2.0.4-preview.1
    - 2.0.5-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 3.0.0-preview
    - 3.1.0-preview
    - 3.3.0-preview
    - 4.0.0-preview
    - 4.0.1-preview
    - 4.1.0-preview
    - 4.2.0-preview
    - 4.3.0-preview
    - 4.6.0-preview
    - 4.8.0-preview
    - 4.9.0-preview
    - 4.10.0-preview
    - 5.0.0-preview
    - 5.1.0
    - 5.2.0
    - 5.2.1
    - 5.2.2
    - 5.2.3
    - 5.3.1
    - 5.6.1
    - 5.7.2
    - 5.8.2
    - 5.9.0
    - 5.10.0
    - 5.13.0
    - 5.16.1
    - 6.5.2
    - 6.5.3
    - 6.7.1
    - 6.9.0
    - 6.9.1
    - 6.9.2
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.13
    - 9.0.0-preview.35
    - 9.0.0-preview.38
    - 9.0.0-preview.60
    - 9.0.0-preview.77
    - 10.0.0-preview.30
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.1.0
    compatible:
    - 17.1.0
    recommended: 17.1.0
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.14
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.collections
    version: 2.4.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: cedf7ca623df7a2048340cebda292590f538da14
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.1.0
    minimumUnityVersion: 6000.1.0a1
- packageId: com.unity.shadergraph@17.1.0
  testable: 0
  isDirectDependency: 0
  version: 17.1.0
  source: 2
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.shadergraph@e5d3455aa133
  assetPath: Packages/com.unity.shadergraph
  name: com.unity.shadergraph
  displayName: Shader Graph
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Shader Graph package adds a visual Shader editing tool to Unity.
    You can use this tool to create Shaders in a visual way instead of writing code.
    Specific render pipelines can implement specific graph features. Currently, both
    the High Definition Rendering Pipeline and the Universal Rendering Pipeline support
    Shader Graph.
  errors: []
  versions:
    all:
    - 0.1.8
    - 0.1.9
    - 0.1.17
    - 1.0.0-beta
    - 1.1.1-preview
    - 1.1.2-preview
    - 1.1.3-preview
    - 1.1.4-preview
    - 1.1.5-preview
    - 1.1.6-preview
    - 1.1.8-preview
    - 1.1.9-preview
    - 2.0.1-preview
    - 2.0.3-preview
    - 2.0.4-preview
    - 2.0.4-preview.1
    - 2.0.5-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 3.0.0-preview
    - 3.1.0-preview
    - 3.3.0-preview
    - 4.0.0-preview
    - 4.0.1-preview
    - 4.1.0-preview
    - 4.2.0-preview
    - 4.3.0-preview
    - 4.6.0-preview
    - 4.8.0-preview
    - 4.9.0-preview
    - 4.10.0-preview
    - 5.0.0-preview
    - 5.1.0
    - 5.2.0
    - 5.2.1
    - 5.2.2
    - 5.2.3
    - 5.3.1
    - 5.6.1
    - 5.7.2
    - 5.8.2
    - 5.9.0
    - 5.10.0
    - 5.13.0
    - 5.16.1
    - 6.5.2
    - 6.5.3
    - 6.7.1
    - 6.9.0
    - 6.9.1
    - 6.9.2
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.14
    - 9.0.0-preview.34
    - 9.0.0-preview.55
    - 9.0.0-preview.72
    - 10.0.0-preview.27
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.1.0
    compatible:
    - 17.1.0
    recommended: 17.1.0
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.searcher
    version: 4.9.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.searcher
    version: 4.9.3
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: e5d3455aa13376f767ad6bf5f3faab2073877176
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.1.0
    minimumUnityVersion: 6000.1.0a1
- packageId: com.unity.render-pipelines.universal-config@17.0.3
  testable: 0
  isDirectDependency: 0
  version: 17.0.3
  source: 2
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.render-pipelines.universal-config@8dc1aab4af1d
  assetPath: Packages/com.unity.render-pipelines.universal-config
  name: com.unity.render-pipelines.universal-config
  displayName: Universal Render Pipeline Config
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Configuration files for the Universal Render Pipeline.
  errors: []
  versions:
    all:
    - 17.0.3
    compatible:
    - 17.0.3
    recommended: 17.0.3
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.1.0
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 8dc1aab4af1d718781689a36ed5231a35ad1a524
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.3
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.nuget.newtonsoft-json@3.2.1
  testable: 0
  isDirectDependency: 0
  version: 3.2.1
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0
  assetPath: Packages/com.unity.nuget.newtonsoft-json
  name: com.unity.nuget.newtonsoft-json
  displayName: Newtonsoft Json
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: 'Newtonsoft Json for use in Unity projects and Unity packages. Currently
    synced to version 13.0.2.


    This package is used for advanced json serialization
    and deserialization. Most Unity users will be better suited using the existing
    json tools built into Unity.

    To avoid assembly clashes, please use this
    package if you intend to use Newtonsoft Json.'
  errors: []
  versions:
    all:
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.0.1-preview.1
    - 1.1.2
    - 2.0.0-preview
    - 2.0.0-preview.1
    - 2.0.0-preview.2
    - 2.0.0
    - 2.0.1-preview.1
    - 2.0.2
    - 3.0.1
    - 3.0.2
    - 3.1.0
    - 3.2.0
    - 3.2.1
    compatible:
    - 3.2.1
    recommended: 3.2.1
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638186318800000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.newtonsoft-json.git
    revision: d8e49aef8979bef617144382052ec2f479645eaf
    path: 
  unityLifecycle:
    version: 3.2.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"* Fixed Newtonsoft DLL when compiling with netstandard
    2.0."}'
  assetStore:
    productId: 
  fingerprint: 74deb55db2a0c29ddfda576608bcb86abbd13ee6
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.2.1
    minimumUnityVersion: 2018.4.0a1
- packageId: com.unity.splines@2.8.1
  testable: 0
  isDirectDependency: 0
  version: 2.8.1
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.splines@b909627b5095
  assetPath: Packages/com.unity.splines
  name: com.unity.splines
  displayName: Splines
  author:
    name: 
    email: 
    url: 
  category: Tool
  type: 
  description: Work with curves and paths. Use the Splines package to generate objects
    and behaviors along paths, create trajectories, and draw shapes.
  errors: []
  versions:
    all:
    - 0.1.0-preview.1
    - 1.0.0-pre.5
    - 1.0.0-pre.7
    - 1.0.0-pre.8
    - 1.0.0-pre.9
    - 1.0.0
    - 1.0.1
    - 2.0.0-pre.2
    - 2.0.0-pre.4
    - 2.0.0
    - 2.1.0
    - 2.2.0
    - 2.2.1
    - 2.3.0
    - 2.4.0
    - 2.5.1
    - 2.5.2
    - 2.6.0
    - 2.6.1
    - 2.7.1
    - 2.7.2
    - 2.8.0
    - 2.8.1
    compatible:
    - 2.8.1
    recommended: 2.8.1
    deprecated: []
  dependencies:
  - name: com.unity.mathematics
    version: 1.2.1
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 1.0.3
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 2.1.0
  keywords:
  - spline
  - curve
  - path
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638822348021470000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.splines@2.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.splines.git
    revision: ae2c33950924058f2912683d16e02268813ce44a
    path: 
  unityLifecycle:
    version: 2.8.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Bug Fixes\n- [SPLB-345] Fixed a bug which was causing
    null reference exceptions during shutdown in IL2CPP builds.\n- [SPLB-337] Fixed
    a bug where `JoinSplinesOnKnots` would throw a null reference exception when
    one of the splines was linked with another spline.\n- [SPLB-341] Fixed a bug
    where changing the tangent mode on a knot on a prefab would not persist when
    entering play mode.\n\n### Changed\n- Internal code cleanup to align with release
    standards."}'
  assetStore:
    productId: 
  fingerprint: b909627b5095061d48761597bbb8384d6f04e510
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.8.1
    minimumUnityVersion: 2022.3.0a1
- packageId: com.unity.profiling.core@1.0.2
  testable: 0
  isDirectDependency: 0
  version: 1.0.2
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.profiling.core@aac7b93912bc
  assetPath: Packages/com.unity.profiling.core
  name: com.unity.profiling.core
  displayName: Unity Profiling Core API
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: The Unity Profiling Core package provides an API for code instrumentation
    markup, and for profiling statistic collection.
  errors: []
  versions:
    all:
    - 0.1.0-preview.1
    - 0.2.0-preview.1
    - 0.2.1-preview.1
    - 1.0.0-pre.1
    - 1.0.0
    - 1.0.2
    compatible:
    - 1.0.2
    recommended: 1.0.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - profiler
  - profiling
  - api
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637828752310000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/profiler.git
    revision: 2189ba14439d76a4083f59fae87163b4bdfd49c2
    path: 
  unityLifecycle:
    version: 1.0.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: aac7b93912bc5df5fe06b04ff1b758493cdc2346
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.2
    minimumUnityVersion: 2020.1.0a1
- packageId: com.unity.scriptablebuildpipeline@2.4.0
  testable: 0
  isDirectDependency: 0
  version: 2.4.0
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.scriptablebuildpipeline@7e8a1cf5a47d
  assetPath: Packages/com.unity.scriptablebuildpipeline
  name: com.unity.scriptablebuildpipeline
  displayName: Scriptable Build Pipeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Scriptable Build Pipeline moves the asset bundle build pipeline
    to C#.  Use the pre-defined build flows, or create your own using the divided
    up APIs.  This system improves build time, fixes incremental build, and provides
    greater flexibility.
  errors: []
  versions:
    all:
    - 0.0.5-preview
    - 0.0.6-preview
    - 0.0.8-preview
    - 0.0.9-preview
    - 0.0.10-preview
    - 0.0.14-preview
    - 0.0.15-preview
    - 0.1.0-preview
    - 0.2.0-preview
    - 1.0.1-preview
    - 1.1.0-preview
    - 1.1.1-preview
    - 1.2.1-preview
    - 1.3.5-preview
    - 1.4.1-preview
    - 1.5.0-preview
    - 1.5.1
    - 1.5.2
    - 1.5.4
    - 1.5.6
    - 1.5.10
    - 1.6.3-preview
    - 1.6.4-preview
    - 1.6.5-preview
    - 1.7.2
    - 1.7.3
    - 1.8.2
    - 1.8.4
    - 1.8.6
    - 1.9.0
    - 1.10.0
    - 1.11.1
    - 1.11.2
    - 1.12.0
    - 1.13.1
    - 1.14.0
    - 1.14.1
    - 1.15.1
    - 1.15.2
    - 1.16.1
    - 1.17.0
    - 1.18.0
    - 1.19.0
    - 1.19.1
    - 1.19.2
    - 1.19.3
    - 1.19.4
    - 1.19.5
    - 1.19.6
    - 1.20.1
    - 1.20.2
    - 1.21.0
    - 1.21.1
    - 1.21.2
    - 1.21.3
    - 1.21.5
    - 1.21.7
    - 1.21.8
    - 1.21.9
    - 1.21.20
    - 1.21.21
    - 1.21.22
    - 1.21.23
    - 1.21.24
    - 1.21.25
    - 1.22.1
    - 1.22.2
    - 1.22.4
    - 1.22.5
    - 2.0.1
    - 2.0.2
    - 2.1.0
    - 2.1.2
    - 2.1.3
    - 2.1.4
    - 2.1.5
    - 2.2.4
    - 2.2.11
    - 2.3.0
    - 2.3.1
    - 2.3.2
    - 2.3.3
    - 2.3.4
    - 2.3.5
    - 2.3.6
    - 2.3.7
    - 2.3.8
    - 2.4.0
    - 2.4.1
    compatible:
    - 2.4.0
    - 2.4.1
    recommended: 2.4.1
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  keywords:
  - build
  - bundle
  - bundles
  - assetbundles
  - cache
  - server
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638830123018510000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.scriptablebuildpipeline@2.4/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/Addressables.git
    revision: 123eb0eba0991600ff0d8fa44d2d4aa9aaa7b981
    path: 
  unityLifecycle:
    version: 2.4.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Avoid recursive dependencies in ContentFiles"}'
  assetStore:
    productId: 
  fingerprint: 7e8a1cf5a47d3fd88c96e0cb21b3d805b63b6834
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.4.0
    minimumUnityVersion: 2023.1.0a1
- packageId: com.unity.searcher@4.9.3
  testable: 0
  isDirectDependency: 0
  version: 4.9.3
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.searcher@1e17ce91558d
  assetPath: Packages/com.unity.searcher
  name: com.unity.searcher
  displayName: Searcher
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: General search window for use in the Editor. First target use is for
    GraphView node search.
  errors: []
  versions:
    all:
    - 4.0.0-preview
    - 4.0.0
    - 4.0.7-preview
    - 4.0.7
    - 4.0.8-preview
    - 4.0.9
    - 4.1.0-preview
    - 4.1.0
    - 4.2.0
    - 4.3.0
    - 4.3.1
    - 4.3.2
    - 4.6.0-preview
    - 4.7.0-preview
    - 4.9.1
    - 4.9.2
    - 4.9.3
    compatible:
    - 4.9.2
    - 4.9.3
    recommended: 4.9.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - search
  - searcher
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638731478077990000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.searcher@4.9/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.searcher.git
    revision: 6fad693b6604ae7175b59ebb4990d9a0b6c1d012
    path: 
  unityLifecycle:
    version: 4.9.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixed a bug where spaces are removed when highlighted.\n-
    Changed VisualSplitter to twoPaneSplitView and updated indentation"}'
  assetStore:
    productId: 
  fingerprint: 1e17ce91558d1d9127554adc03d275f39a7466a2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 4.9.2
    minimumUnityVersion: 2019.1.0a1
- packageId: com.unity.burst@1.8.23
  testable: 0
  isDirectDependency: 0
  version: 1.8.23
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.burst@6aff1dd08a0c
  assetPath: Packages/com.unity.burst
  name: com.unity.burst
  displayName: Burst
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Burst is a compiler that translates from IL/.NET bytecode to highly
    optimized native code using LLVM.
  errors: []
  versions:
    all:
    - 0.2.4-preview.5
    - 0.2.4-preview.7
    - 0.2.4-preview.11
    - 0.2.4-preview.12
    - 0.2.4-preview.13
    - 0.2.4-preview.14
    - 0.2.4-preview.15
    - 0.2.4-preview.16
    - 0.2.4-preview.17
    - 0.2.4-preview.18
    - 0.2.4-preview.19
    - 0.2.4-preview.20
    - 0.2.4-preview.21
    - 0.2.4-preview.22
    - 0.2.4-preview.23
    - 0.2.4-preview.24
    - 0.2.4-preview.25
    - 0.2.4-preview.30
    - 0.2.4-preview.31
    - 0.2.4-preview.33
    - 0.2.4-preview.34
    - 0.2.4-preview.37
    - 0.2.4-preview.41
    - 0.2.4-preview.45
    - 0.2.4-preview.48
    - 0.2.4-preview.50
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.0-preview.4
    - 1.1.1
    - 1.1.2
    - 1.1.3-preview.3
    - 1.2.0-preview.1
    - 1.2.0-preview.5
    - 1.2.0-preview.6
    - 1.2.0-preview.8
    - 1.2.0-preview.9
    - 1.2.0-preview.10
    - 1.2.0-preview.11
    - 1.2.0-preview.12
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.3.0-preview.1
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.4
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0-preview.8
    - 1.3.0-preview.9
    - 1.3.0-preview.10
    - 1.3.0-preview.11
    - 1.3.0-preview.12
    - 1.3.0-preview.13
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0-pre.1
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.4
    - 1.4.0-preview.5
    - 1.4.1-pre.1
    - 1.4.1-pre.2
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4-preview.1
    - 1.4.4-preview.2
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.4.9
    - 1.4.11
    - 1.5.0-pre.3
    - 1.5.0-pre.4
    - 1.5.0-pre.5
    - 1.5.0
    - 1.5.1
    - 1.5.2
    - 1.5.3
    - 1.5.4
    - 1.5.5
    - 1.5.6-preview.1
    - 1.5.6
    - 1.6.0-pre.2
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.6.6
    - 1.7.0-pre.1
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.7
    - 1.8.8
    - 1.8.9
    - 1.8.10
    - 1.8.11
    - 1.8.12
    - 1.8.13
    - 1.8.14
    - 1.8.15
    - 1.8.16
    - 1.8.17
    - 1.8.18
    - 1.8.19
    - 1.8.20
    - 1.8.21
    - 1.8.22
    - 1.8.23
    - 1.8.24
    compatible:
    - 1.8.23
    - 1.8.24
    recommended: 1.8.24
    deprecated: []
  dependencies:
  - name: com.unity.mathematics
    version: 1.2.1
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638856629417660000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.burst@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/burst.git
    revision: 0a4fca7c4d2ed5ed3ce7fd57c6944390dd4745a7
    path: 
  unityLifecycle:
    version: 1.8.23
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- Fixed \"Building for ''visionOS-simulator'',
    but linking in object file built for ''visionOS''\" errors that could occur in
    1.8.22\n- Fixed crash that could occur during Burst compilation for methods with
    `Span<T>` or `ReadOnlySpan<T>` parameter types\n- Fixed Burst compilation error
    related to `Aliasing.ExpectNotAliased` failing because of unexpected aliasing
    of `[NativeContainer]` types\n\n### Changed\n- On QNX and Embedded Linux, the
    `BurstDebugInformation_DoNotShip` folder is now prefixed with the player build
    directory name instead of the product name."}'
  assetStore:
    productId: 
  fingerprint: 6aff1dd08a0c2f92ddb7f56ec033a5cb88967056
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.23
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.mathematics@1.3.2
  testable: 0
  isDirectDependency: 0
  version: 1.3.2
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.mathematics@8017b507cc74
  assetPath: Packages/com.unity.mathematics
  name: com.unity.mathematics
  displayName: Mathematics
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Unity's C# SIMD math library providing vector types and math functions
    with a shader like syntax.
  errors: []
  versions:
    all:
    - 0.0.12-preview.2
    - 0.0.12-preview.5
    - 0.0.12-preview.8
    - 0.0.12-preview.10
    - 0.0.12-preview.11
    - 0.0.12-preview.13
    - 0.0.12-preview.17
    - 0.0.12-preview.19
    - 0.0.12-preview.20
    - 1.0.0-preview.1
    - 1.0.1
    - 1.1.0-preview.1
    - 1.1.0
    - 1.2.1
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.3.1
    - 1.3.2
    compatible:
    - 1.3.2
    recommended: 1.3.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638409134840000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.mathematics@1.3/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/Unity.Mathematics.git
    revision: 1695a8503482a3131be78cc26308a93f82c05b04
    path: 
  unityLifecycle:
    version: 1.3.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n* Fixed `math.hash` crash when using IL2CPP
    builds on Arm 32 bit devices.\n* Fixed obsolete method usage warnings for `MatrixDrawer.CanCacheInspectorGUI`
    and `PrimitiveVectorDrawer.CanCacheInspectorGUI` in UNITY_2023_2_OR_NEWER.\n*
    Updated minimum editor version to 2021.3"}'
  assetStore:
    productId: 
  fingerprint: 8017b507cc74bf0a1dd14b18aa860569f807314d
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.3.2
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.collections@2.5.1
  testable: 0
  isDirectDependency: 0
  version: 2.5.1
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.collections@56bff8827a7e
  assetPath: Packages/com.unity.collections
  name: com.unity.collections
  displayName: Collections
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A C# collections library providing data structures that can be used
    in jobs, and optimized by Burst compiler.
  errors: []
  versions:
    all:
    - 0.0.9-preview.1
    - 0.0.9-preview.2
    - 0.0.9-preview.3
    - 0.0.9-preview.4
    - 0.0.9-preview.5
    - 0.0.9-preview.6
    - 0.0.9-preview.7
    - 0.0.9-preview.8
    - 0.0.9-preview.9
    - 0.0.9-preview.10
    - 0.0.9-preview.11
    - 0.0.9-preview.12
    - 0.0.9-preview.13
    - 0.0.9-preview.14
    - 0.0.9-preview.15
    - 0.0.9-preview.16
    - 0.0.9-preview.17
    - 0.0.9-preview.18
    - 0.0.9-preview.19
    - 0.0.9-preview.20
    - 0.1.0-preview
    - 0.1.1-preview
    - 0.2.0-preview.13
    - 0.3.0-preview.0
    - 0.4.0-preview.6
    - 0.5.0-preview.9
    - 0.5.1-preview.11
    - 0.5.2-preview.8
    - 0.6.0-preview.9
    - 0.7.0-preview.2
    - 0.7.1-preview.3
    - 0.8.0-preview.5
    - 0.9.0-preview.5
    - 0.9.0-preview.6
    - 0.11.0-preview.17
    - 0.12.0-preview.13
    - 0.14.0-preview.16
    - 0.15.0-preview.21
    - 0.17.0-preview.18
    - 1.0.0-pre.3
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.1.0
    - 1.2.3-pre.1
    - 1.2.3
    - 1.2.4
    - 1.3.1
    - 1.4.0
    - 1.5.1
    - 1.5.2
    - 2.1.0-exp.4
    - 2.1.0-pre.2
    - 2.1.0-pre.6
    - 2.1.0-pre.11
    - 2.1.0-pre.18
    - 2.1.1
    - 2.1.4
    - 2.2.0
    - 2.2.1
    - 2.3.0-exp.1
    - 2.3.0-pre.3
    - 2.4.0-exp.2
    - 2.4.0-pre.2
    - 2.4.0-pre.5
    - 2.4.0
    - 2.4.1
    - 2.4.2
    - 2.4.3
    - 2.5.0-exp.1
    - 2.5.0-pre.2
    - 2.5.1
    - 2.5.2
    - 2.5.3
    - 2.5.7
    - 2.6.0-exp.2
    - 2.6.0-pre.3
    - 2.6.0-pre.4
    compatible:
    - 2.5.1
    - 2.5.2
    - 2.5.3
    - 2.5.7
    - 2.6.0-exp.2
    - 2.6.0-pre.3
    - 2.6.0-pre.4
    recommended: 2.5.7
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.17
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  keywords:
  - dots
  - collections
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638621282722800000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collections@2.5/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/dots.git
    revision: 5b0dea6b455f5df005c19fa984ddfa237d6cd707
    path: 
  unityLifecycle:
    version: 2.5.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n* Updated Burst dependency to version
    1.8.17\n* Updated Unity Test Framework dependency to version 1.4.5\n* Updated
    entities packages dependencies\n\n### Fixed\n* Certain cases would cause an ILPostProcessor
    to fail, blocking compilation, but no more."}'
  assetStore:
    productId: 
  fingerprint: 56bff8827a7ef6d44fcee4f36e558a74da89c1a0
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.5.1
    minimumUnityVersion: 2022.3.11f1
- packageId: com.unity.rendering.light-transport@1.0.1
  testable: 0
  isDirectDependency: 0
  version: 1.0.1
  source: 2
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.rendering.light-transport@e647573c7d2a
  assetPath: Packages/com.unity.rendering.light-transport
  name: com.unity.rendering.light-transport
  displayName: Unity Light Transport Library
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Unity Light Transport Library exposes reusable code for writing light
    transport algorithms such as raytracing or pathtracing
  errors: []
  versions:
    all:
    - 1.0.1
    compatible:
    - 1.0.1
    recommended: 1.0.1
    deprecated: []
  dependencies:
  - name: com.unity.collections
    version: 2.2.0
  - name: com.unity.mathematics
    version: 1.2.4
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.burst
    version: 1.8.23
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.1.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords:
  - raytracing
  - pathtracing
  - monte-carlo
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: e647573c7d2ae78386ecb3f9f962738597f13fcf
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.1
    minimumUnityVersion: 2023.3.0b1
- packageId: com.unity.settings-manager@2.1.0
  testable: 0
  isDirectDependency: 0
  version: 2.1.0
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.settings-manager@41738c275190
  assetPath: Packages/com.unity.settings-manager
  name: com.unity.settings-manager
  displayName: Settings Manager
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A framework for making any serializable field a setting, complete
    with a pre-built settings interface.
  errors: []
  versions:
    all:
    - 0.1.0-preview.4
    - 0.1.0-preview.8
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 2.0.0
    - 2.0.1
    - 2.1.0
    compatible:
    - 2.1.0
    recommended: 2.1.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638792046006940000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.settings-manager@2.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.settings-manager.git
    revision: 6eb5ed85eaf3276cc09f0cc4fc3311c72f0fc196
    path: 
  unityLifecycle:
    version: 2.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changes\n\n- Enable Auto Reference on main assembly.\n\n###
    Bug Fixes\n\n- [case: PBLD-56] Added tooltip to \"Options\" gear icon to specify
    that context menu items are relevant only to the selected category."}'
  assetStore:
    productId: 
  fingerprint: 41738c27519039c335849eb78949382f4d7a3544
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.1.0
    minimumUnityVersion: 2022.3.0a1
- packageId: com.unity.nuget.mono-cecil@1.11.4
  testable: 0
  isDirectDependency: 0
  version: 1.11.4
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f
  assetPath: Packages/com.unity.nuget.mono-cecil
  name: com.unity.nuget.mono-cecil
  displayName: Mono Cecil
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: 'The mono cecil library from https://www.nuget.org/packages/Mono.Cecil/


    This
    package is intended for internal Unity use only. Most Unity users will be better
    suite using the existing community tooling.

    To avoid assembly clashes, please
    use this package if you intend to use Mono.Cecil.'
  errors: []
  versions:
    all:
    - 0.1.6-preview.2
    - 1.0.0-preview.1
    - 1.10.0-preview.1
    - 1.10.1-preview.1
    - 1.10.1
    - 1.10.2
    - 1.11.4
    - 1.11.5
    compatible:
    - 1.11.4
    - 1.11.5
    recommended: 1.11.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637852971930000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.mono-cecil.git
    revision: d0133ce672d724694b56bfd20672acf6f8737fec
    path: 
  unityLifecycle:
    version: 1.11.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.11.4
    minimumUnityVersion: 2018.4.0a1
- packageId: com.unity.test-framework.performance@3.1.0
  testable: 0
  isDirectDependency: 0
  version: 3.1.0
  source: 1
  resolvedPath: C:\Projects\Unity\ProjectCourt\Library\PackageCache\com.unity.test-framework.performance@92d1d09a72ed
  assetPath: Packages/com.unity.test-framework.performance
  name: com.unity.test-framework.performance
  displayName: Performance testing API
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Package that extends Unity Test Framework package. Adds performance
    testing capabilities and collects configuration metadata.
  errors: []
  versions:
    all:
    - 0.1.27-preview
    - 0.1.29-preview
    - 0.1.31-preview
    - 0.1.33-preview
    - 0.1.34-preview
    - 0.1.36-preview
    - 0.1.37-preview
    - 0.1.39-preview
    - 0.1.40-preview
    - 0.1.41-preview
    - 0.1.42-preview
    - 0.1.44-preview
    - 0.1.45-preview
    - 0.1.47-preview
    - 0.1.48-preview
    - 0.1.49-preview
    - 0.1.50-preview
    - 1.0.4-preview
    - 1.0.6-preview
    - 1.0.9-preview
    - 1.1.2-preview
    - 1.2.0-preview
    - 1.2.1-preview
    - 1.2.3-preview
    - 1.2.5-preview
    - 1.2.6-preview
    - 1.3.0-preview
    - 1.3.1-preview
    - 1.3.2-preview
    - 1.3.3-preview
    - 2.0.1-preview
    - 2.0.2-preview
    - 2.0.3-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 2.0.9-preview
    - 2.1.0-preview
    - 2.2.0-preview
    - 2.3.1-preview
    - 2.4.1-preview
    - 2.5.1-preview
    - 2.6.0-preview
    - 2.7.0-preview
    - 2.8.0-preview
    - 2.8.1-preview
    - 3.0.0-pre.1
    - 3.0.0-pre.2
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.1.0
    compatible:
    - 3.1.0
    recommended: 3.1.0
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.33
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - performance
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638792643567670000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.test-framework.performance@3.1/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.test-framework.performance.git
    revision: 6126c0ee357019ed762100ffeca520029274e869
    path: 
  unityLifecycle:
    version: 3.1.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n- Added an optional command-line argument
    \"perfTestResults\" to control the target location of performance test run results
    file.\n### Fixed\n- Warmup cycles no longer record GC measurements.\n- Setup
    and Cleanup cycles no longer contribute to GC measurements."}'
  assetStore:
    productId: 
  fingerprint: 92d1d09a72ed696fa23fd76c675b29d211664b50
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.1.0
    minimumUnityVersion: 2020.3.0a1
m_BuiltInPackagesHash: 8d5c9b497825795d3f5671ef3fc8e5cbc8574cc5
