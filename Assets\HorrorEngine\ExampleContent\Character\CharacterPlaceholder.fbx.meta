fileFormatVersion: 2
guid: 3f27c77ae52e1074cbbe47acddea4495
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      74: -7361427651036151181
    second: Idle
  - first:
      74: -8274178093218619139
    second: KnifeAttack
  - first:
      74: -5480419611278045171
    second: Run
  - first:
      74: -4397452649422353911
    second: KnifeReady
  - first:
      74: -6890646250042638274
    second: GunReady
  - first:
      74: -8139625916263470543
    second: GunFire
  - first:
      74: -5124320824457396573
    second: GunFire
  externalObjects: {}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Idle
      takeName: rig|Idle
      internalID: 0
      firstFrame: 0
      lastFrame: 49
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeAttack
      takeName: rig|KnifeAttack
      internalID: 0
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeReady
      takeName: rig|KnifeAttack
      internalID: 0
      firstFrame: 20
      lastFrame: 21
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: GunReady
      takeName: rig|Shooting
      internalID: 0
      firstFrame: 14
      lastFrame: 15
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: GunFire
      takeName: rig|Shooting
      internalID: 0
      firstFrame: 0
      lastFrame: 14
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: DEF-spine
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-thigh.L
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-thigh.R
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-shin.L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-shin.R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-foot.L
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-foot.R
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-spine.001
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-spine.002
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-shoulder.L
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-shoulder.R
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-upper_arm.L
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-upper_arm.R
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-forearm.L
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-forearm.R
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-hand.L
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-hand.R
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-toe.L
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-toe.R
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: DEF-spine.003
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: CharacterPlaceholder(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character
      parentName: CharacterPlaceholder(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: rig
      parentName: CharacterPlaceholder(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 100, y: 100, z: 100}
    - name: root
      parentName: rig
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: DEF-spine
      parentName: root
      position: {x: -0, y: 0.0005519999, z: 0.010098999}
      rotation: {x: 0.7904554, y: 0, z: -0, w: 0.6125196}
      scale: {x: 1, y: 1, z: 1}
    - name: DEF-spine.001
      parentName: DEF-spine
      position: {x: -0, y: 0.0015221953, z: 9.313225e-11}
      rotation: {x: -0.06447638, y: 0, z: -0, w: 0.99791926}
      scale: {x: 1, y: 0.9999999, z: 0.9999999}
    - name: DEF-spine.002
      parentName: DEF-spine.001
      position: {x: -0, y: 0.0013663665, z: 1.5832484e-10}
      rotation: {x: -0.077464126, y: 0, z: -0, w: 0.99699515}
      scale: {x: 1, y: 1.0000002, z: 1.0000001}
    - name: DEF-spine.003
      parentName: DEF-spine.002
      position: {x: -0, y: 0.001728875, z: -1.3969838e-11}
      rotation: {x: 0.0016272482, y: 0, z: -0, w: 0.9999987}
      scale: {x: 1, y: 0.9999996, z: 0.99999976}
    - name: DEF-shoulder.L
      parentName: DEF-spine.003
      position: {x: -0.000183, y: 0.0013722109, z: 0.00078250933}
      rotation: {x: -0.5659591, y: 0.40879968, z: 0.39755976, w: 0.59541535}
      scale: {x: 0.99999994, y: 1, z: 0.99999994}
    - name: DEF-upper_arm.L
      parentName: DEF-shoulder.L
      position: {x: -0.00007791538, y: 0.0020079091, z: -0.0002038485}
      rotation: {x: 0.16612285, y: -0.7796923, z: 0.13577028, w: 0.5882599}
      scale: {x: 0.98379976, y: 1.0332052, z: 0.9837997}
    - name: DEF-forearm.L
      parentName: DEF-upper_arm.L
      position: {x: 1.6763806e-10, y: 0.002885098, z: -3.4924597e-10}
      rotation: {x: 0.0036679262, y: 0.02585975, z: -0.000099895646, w: 0.9996589}
      scale: {x: 0.9984038, y: 1.0032027, z: 0.99840134}
    - name: DEF-hand.L
      parentName: DEF-forearm.L
      position: {x: -2.2817402e-10, y: 0.0026283558, z: -1.862645e-10}
      rotation: {x: -0.0086307265, y: -0.0008039911, z: 0.00722582, w: 0.99993634}
      scale: {x: 1.0180813, y: 0.96480244, z: 1.0180768}
    - name: DEF-hand.L_end
      parentName: DEF-hand.L
      position: {x: -0, y: 0.00080160797, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: DEF-shoulder.R
      parentName: DEF-spine.003
      position: {x: 0.000183, y: 0.0013722109, z: 0.00078250933}
      rotation: {x: -0.5659591, y: -0.4087996, z: -0.39755976, w: 0.59541535}
      scale: {x: 0.99999994, y: 1, z: 0.99999994}
    - name: DEF-upper_arm.R
      parentName: DEF-shoulder.R
      position: {x: 0.00007791564, y: 0.0020079084, z: -0.0002038473}
      rotation: {x: 0.16670837, y: 0.78006196, z: -0.13471515, w: 0.5878465}
      scale: {x: 0.98379976, y: 1.0332056, z: 0.9837996}
    - name: DEF-forearm.R
      parentName: DEF-upper_arm.R
      position: {x: -0.0000000022212043, y: 0.002885098, z: -1.2340023e-10}
      rotation: {x: 0.0036680542, y: -0.025965383, z: 0.000100216246, w: 0.99965614}
      scale: {x: 0.9984038, y: 1.0032024, z: 0.9984014}
    - name: DEF-hand.R
      parentName: DEF-forearm.R
      position: {x: 0.000000014743136, y: 0.0026283541, z: 0.0000000040373345}
      rotation: {x: 0.00958926, y: 0.0005005746, z: -0.04012195, w: 0.9991487}
      scale: {x: 1.018092, y: 0.964795, z: 1.0180731}
    - name: DEF-hand.R_end
      parentName: DEF-hand.R
      position: {x: -0, y: 0.00080160797, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: DEF-spine.004
      parentName: DEF-spine.003
      position: {x: -0, y: 0.0019257857, z: 9.3132255e-12}
      rotation: {x: 0.20173797, y: 0, z: -0, w: 0.97943956}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: DEF-neck
      parentName: DEF-spine.004
      position: {x: -0, y: 0.0006616351, z: -2.7008354e-10}
      rotation: {x: -0.094857745, y: 0, z: -0, w: 0.99549085}
      scale: {x: 1, y: 1, z: 1}
    - name: DEF-head
      parentName: DEF-neck
      position: {x: -0, y: 0.0006270137, z: -1.862645e-10}
      rotation: {x: -0.09371185, y: 0, z: -0, w: 0.9955994}
      scale: {x: 1, y: 1, z: 0.9999999}
    - name: DEF-head_end
      parentName: DEF-head
      position: {x: -0, y: 0.001983, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: DEF-thigh.L
      parentName: DEF-spine
      position: {x: -0.0009799999, y: 0.0007081852, z: 0.00025942287}
      rotation: {x: 0.9865149, y: 0.000000006851724, z: -0.0000000011367624, w: 0.16367121}
      scale: {x: 1, y: 0.9999999, z: 0.99999994}
    - name: DEF-shin.L
      parentName: DEF-thigh.L
      position: {x: -7.491241e-11, y: 0.0053636925, z: -1.8626444e-11}
      rotation: {x: 0.08754083, y: 5.8049426e-10, z: 0.0000000031437275, w: 0.996161}
      scale: {x: 1, y: 0.9999999, z: 0.9999998}
    - name: DEF-foot.L
      parentName: DEF-shin.L
      position: {x: 6.4280053e-12, y: 0.0045421463, z: -1.7925019e-11}
      rotation: {x: -0.52736276, y: 0.00049743266, z: -0.0003086811, w: 0.8496401}
      scale: {x: 1, y: 1, z: 0.9999995}
    - name: DEF-toe.L
      parentName: DEF-foot.L
      position: {x: -1.6079867e-11, y: 0.001292452, z: 5.0858942e-11}
      rotation: {x: 0.00016143828, y: 0.9612484, z: -0.27568316, w: 0.0005626924}
      scale: {x: 0.99999994, y: 1, z: 1.0000002}
    - name: DEF-toe.L_end
      parentName: DEF-toe.L
      position: {x: -0, y: 0.00067200005, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: DEF-thigh.R
      parentName: DEF-spine
      position: {x: 0.0009799999, y: 0.0007081852, z: 0.00025942287}
      rotation: {x: 0.9865149, y: -0.000000006851724, z: 0.0000000011367624, w: 0.16367121}
      scale: {x: 1, y: 0.9999999, z: 0.99999994}
    - name: DEF-shin.R
      parentName: DEF-thigh.R
      position: {x: 7.491241e-11, y: 0.0053636925, z: -1.8626444e-11}
      rotation: {x: 0.08754083, y: -5.8049426e-10, z: -0.0000000031437275, w: 0.996161}
      scale: {x: 1, y: 0.9999999, z: 0.9999998}
    - name: DEF-foot.R
      parentName: DEF-shin.R
      position: {x: -6.4280053e-12, y: 0.0045421463, z: -1.7925019e-11}
      rotation: {x: -0.52736276, y: 0.0004973752, z: -0.00030878582, w: 0.8496401}
      scale: {x: 1, y: 1, z: 0.9999995}
    - name: DEF-toe.R
      parentName: DEF-foot.R
      position: {x: 1.1970769e-10, y: 0.001292452, z: 7.952621e-11}
      rotation: {x: 0.00016132747, y: 0.9612484, z: -0.27568316, w: 0.0005627242}
      scale: {x: 0.99999994, y: 1, z: 1.0000002}
    - name: DEF-toe.R_end
      parentName: DEF-toe.R
      position: {x: -0, y: 0.00067200005, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
