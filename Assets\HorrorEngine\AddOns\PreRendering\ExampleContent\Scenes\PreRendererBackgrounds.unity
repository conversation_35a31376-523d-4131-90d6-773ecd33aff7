%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!4 &65569773 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
  m_PrefabInstance: {fileID: 1272699805}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &65569774 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 2618498201448163646, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
  m_PrefabInstance: {fileID: 1272699805}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a8a881b5bf711fc499d4b5ff3d004b9f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &112061618
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 112061619}
  - component: {fileID: 112061620}
  m_Layer: 0
  m_Name: Floor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &112061619
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 112061618}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.37999487, y: 0.16, z: 14.37479}
  m_LocalScale: {x: 8.3938, y: 1, z: 28.079393}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1701055343}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &112061620
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 112061618}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1001 &162087929
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 691661738}
    m_Modifications:
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: Name
      value: CameraHAL
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: ColorTexture
      value: 
      objectReference: {fileID: 2800000, guid: 5d53085ece75b864683b41995086a457, type: 3}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_RenderToColor
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LightingCapture
      value: 
      objectReference: {fileID: 833275729}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: DepthRenderTexture
      value: 
      objectReference: {fileID: 8400000, guid: a715de0266875b042805808ea6f009c3, type: 2}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_RuntimeEffects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: 'm_RuntimeEffects.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 1f4eb235c9a84d94e90567612df7f6fc, type: 2}
    - target: {fileID: 8196853866405996373, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_Name
      value: PreRenderedCam (1)
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 8196853866405996373, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 683600442}
    - targetCorrespondingSourceObject: {fileID: 8196853866405996373, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 683600441}
  m_SourcePrefab: {fileID: 100100000, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
--- !u!4 &162087930 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
  m_PrefabInstance: {fileID: 162087929}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &171055927
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 171055928}
  m_Layer: 0
  m_Name: ------- CAMERAS
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &171055928
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 171055927}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -3.6755853, y: 2.1958323, z: 3.8343506}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1218234836}
  - {fileID: 492397743}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &220675694
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 171055928}
    m_Modifications:
    - target: {fileID: 1191269246897273220, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.x
      value: 5.95
      objectReference: {fileID: 0}
    - target: {fileID: 1191269246897273220, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.66
      objectReference: {fileID: 0}
    - target: {fileID: 1191269246897273220, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.z
      value: 1.76
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.Dutch
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.FieldOfView
      value: 61.927517
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.FarClipPlane
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.ModeOverride
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.NearClipPlane
      value: 0.099999994
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.m_SensorSize.x
      value: 36
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.m_SensorSize.y
      value: 24
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.OrthographicSize
      value: 0.006
      objectReference: {fileID: 0}
    - target: {fileID: 2471370469874519972, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 4294967287
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.FieldOfView
      value: 61.927517
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.FarClipPlane
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.ModeOverride
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.NearClipPlane
      value: 0.2598474
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.OrthographicSize
      value: 0.015590845
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.PhysicalProperties.SensorSize.x
      value: 36
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.PhysicalProperties.SensorSize.y
      value: 24
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.PhysicalProperties.FocusDistance
      value: 2598.4739
      objectReference: {fileID: 0}
    - target: {fileID: 5130916788458517775, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Size.x
      value: 9.24
      objectReference: {fileID: 0}
    - target: {fileID: 5130916788458517775, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Size.y
      value: 7.68
      objectReference: {fileID: 0}
    - target: {fileID: 5130916788458517775, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Size.z
      value: 21.26
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.x
      value: 3.6755853
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.6558323
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.1343505
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.6875782
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.32405496
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.z
      value: -5.766183
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.91679955
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.06153039
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.3936937
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.026419902
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 7.88
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 46.479
      objectReference: {fileID: 0}
    - target: {fileID: 7888032800186445521, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Name
      value: CameraCorridor
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 65569773}
    - targetCorrespondingSourceObject: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1017147257}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 8607145438657436085, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 506901169}
    - targetCorrespondingSourceObject: {fileID: 2471370469874519972, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 220675696}
  m_SourcePrefab: {fileID: 100100000, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
--- !u!1 &220675695 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2471370469874519972, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
  m_PrefabInstance: {fileID: 220675694}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &220675696
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 220675695}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1e3fdca004f2d45fe8abbed571a8abd5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SerializedVersion: 0
  m_OverrideArea: 0
  m_Area: 0
  m_OverrideGenerateLinks: 1
  m_GenerateLinks: 1
  m_IgnoreFromBuild: 0
  m_ApplyToChildren: 1
  m_AffectedAgents: ffffffff
--- !u!1001 &269121494
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 492397743}
    m_Modifications:
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalScale.x
      value: 11.649
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalScale.y
      value: 11.649
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalScale.z
      value: 11.649
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.02
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.9
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalPosition.z
      value: 11.64
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071071
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.7071065
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.00000615698
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.0000061569854
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 9184288872504641143, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_Name
      value: LightCapture
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
--- !u!1 &300825858
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 300825859}
  - component: {fileID: 300825860}
  m_Layer: 0
  m_Name: HAL
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &300825859
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 300825858}
  serializedVersion: 2
  m_LocalRotation: {x: 0.49999997, y: -0.49999997, z: -0.50000006, w: 0.5000001}
  m_LocalPosition: {x: -0.356, y: 1.9, z: 21.779}
  m_LocalScale: {x: 8.3938, y: 1, z: 2.1114342}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1701055343}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: -90}
--- !u!65 &300825860
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 300825858}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &337757317
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 337757318}
  - component: {fileID: 337757319}
  m_Layer: 0
  m_Name: WAll (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &337757318
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 337757317}
  serializedVersion: 2
  m_LocalRotation: {x: 0.49999997, y: -0.49999997, z: -0.50000006, w: 0.5000001}
  m_LocalPosition: {x: 0, y: 1.9, z: 22.25}
  m_LocalScale: {x: 8.3938, y: 1, z: 6.437201}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1701055343}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: -90}
--- !u!65 &337757319
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 337757317}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &450381529 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8196853866405996373, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
  m_PrefabInstance: {fileID: 1017147256}
  m_PrefabAsset: {fileID: 0}
--- !u!95 &450381534
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 450381529}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: b8888dec534db4c4cb6895cf758cd2c2, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &450381538
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 450381529}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c921f10d2cef435468fb06032f0b42ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Property: _OverlayOpacity
  m_Value: 0
--- !u!4 &492397743 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
  m_PrefabInstance: {fileID: 1389159341}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &506901167 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8607145438657436085, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
  m_PrefabInstance: {fileID: 220675694}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &506901168 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
  m_PrefabInstance: {fileID: 220675694}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &506901169
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 506901167}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d30cf7f8506435e48b475044a49393d6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Camera: {fileID: 1396297104}
--- !u!1 &536016432
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 536016435}
  - component: {fileID: 536016434}
  - component: {fileID: 536016433}
  m_Layer: 0
  m_Name: Particle System
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!199 &536016433
ParticleSystemRenderer:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 536016432}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10306, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_RenderMode: 0
  m_MeshDistribution: 0
  m_SortMode: 0
  m_MinParticleSize: 0
  m_MaxParticleSize: 0.5
  m_CameraVelocityScale: 0
  m_VelocityScale: 0
  m_LengthScale: 2
  m_SortingFudge: 0
  m_NormalDirection: 1
  m_ShadowBias: 0
  m_RenderAlignment: 0
  m_Pivot: {x: 0, y: 0, z: 0}
  m_Flip: {x: 0, y: 0, z: 0}
  m_EnableGPUInstancing: 1
  m_ApplyActiveColorSpace: 1
  m_AllowRoll: 1
  m_FreeformStretching: 0
  m_RotateWithStretchDirection: 1
  m_UseCustomVertexStreams: 0
  m_VertexStreams: 00010304
  m_UseCustomTrailVertexStreams: 0
  m_TrailVertexStreams: 00010304
  m_Mesh: {fileID: 0}
  m_Mesh1: {fileID: 0}
  m_Mesh2: {fileID: 0}
  m_Mesh3: {fileID: 0}
  m_MeshWeighting: 1
  m_MeshWeighting1: 1
  m_MeshWeighting2: 1
  m_MeshWeighting3: 1
  m_MaskInteraction: 0
--- !u!198 &536016434
ParticleSystem:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 536016432}
  serializedVersion: 8
  lengthInSec: 5
  simulationSpeed: 1
  stopAction: 0
  cullingMode: 0
  ringBufferMode: 0
  ringBufferLoopRange: {x: 0, y: 1}
  emitterVelocityMode: 1
  looping: 1
  prewarm: 0
  playOnAwake: 1
  useUnscaledTime: 0
  autoRandomSeed: 1
  startDelay:
    serializedVersion: 2
    minMaxState: 0
    scalar: 0
    minScalar: 0
    maxCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    minCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  moveWithTransform: 0
  moveWithCustomTransform: {fileID: 0}
  scalingMode: 1
  randomSeed: 0
  InitialModule:
    serializedVersion: 3
    enabled: 1
    startLifetime:
      serializedVersion: 2
      minMaxState: 3
      scalar: 5
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSpeed:
      serializedVersion: 2
      minMaxState: 3
      scalar: 3
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startColor:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    startSize:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.025
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSizeY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSizeZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotationX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotationY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotation:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    randomizeRotationDirection: 0
    gravitySource: 0
    maxNumParticles: 1000
    customEmitterVelocity: {x: 0, y: 0, z: 0}
    size3D: 0
    rotation3D: 0
    gravityModifier:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  ShapeModule:
    serializedVersion: 6
    enabled: 1
    type: 0
    angle: 5
    length: 5
    boxThickness: {x: 0, y: 0, z: 0}
    radiusThickness: 1
    donutRadius: 0.2
    m_Position: {x: 0, y: 0, z: 0}
    m_Rotation: {x: 0, y: 0, z: 0}
    m_Scale: {x: 1, y: 1, z: 1}
    placementMode: 0
    m_MeshMaterialIndex: 0
    m_MeshNormalOffset: 0
    m_MeshSpawn:
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    m_Mesh: {fileID: 0}
    m_MeshRenderer: {fileID: 0}
    m_SkinnedMeshRenderer: {fileID: 0}
    m_Sprite: {fileID: 0}
    m_SpriteRenderer: {fileID: 0}
    m_UseMeshMaterialIndex: 0
    m_UseMeshColors: 1
    alignToDirection: 0
    m_Texture: {fileID: 0}
    m_TextureClipChannel: 3
    m_TextureClipThreshold: 0
    m_TextureUVChannel: 0
    m_TextureColorAffectsParticles: 1
    m_TextureAlphaAffectsParticles: 1
    m_TextureBilinearFiltering: 0
    randomDirectionAmount: 0
    sphericalDirectionAmount: 0
    randomPositionAmount: 0
    radius:
      value: 0.01
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    arc:
      value: 360
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
  EmissionModule:
    enabled: 1
    serializedVersion: 4
    rateOverTime:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 10
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    rateOverDistance:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_BurstCount: 2
    m_Bursts:
    - serializedVersion: 2
      time: 0
      countCurve:
        serializedVersion: 2
        minMaxState: 0
        scalar: 5
        minScalar: 30
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
      cycleCount: 1
      repeatInterval: 0.01
      probability: 1
    - serializedVersion: 2
      time: 1
      countCurve:
        serializedVersion: 2
        minMaxState: 0
        scalar: 10
        minScalar: 30
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
      cycleCount: 1
      repeatInterval: 0.01
      probability: 1
  SizeModule:
    enabled: 0
    curve:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
  RotationModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.7853982
      minScalar: 0.7853982
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
  ColorModule:
    enabled: 0
    gradient:
      serializedVersion: 2
      minMaxState: 1
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
  UVModule:
    serializedVersion: 2
    enabled: 0
    mode: 0
    timeMode: 0
    fps: 30
    frameOverTime:
      serializedVersion: 2
      minMaxState: 1
      scalar: 0.9999
      minScalar: 0.9999
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startFrame:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    speedRange: {x: 0, y: 1}
    tilesX: 1
    tilesY: 1
    animationType: 0
    rowIndex: 0
    cycles: 1
    uvChannelMask: -1
    rowMode: 1
    sprites:
    - sprite: {fileID: 0}
    flipU: 0
    flipV: 0
  VelocityModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    radial:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    speedModifier:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    inWorldSpace: 0
  InheritVelocityModule:
    enabled: 0
    m_Mode: 0
    m_Curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  LifetimeByEmitterSpeedModule:
    enabled: 0
    m_Curve:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: -0.8
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0.2
          inSlope: -0.8
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_Range: {x: 0, y: 1}
  ForceModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    inWorldSpace: 0
    randomizePerFrame: 0
  ExternalForcesModule:
    serializedVersion: 2
    enabled: 0
    multiplierCurve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    influenceFilter: 0
    influenceMask:
      serializedVersion: 2
      m_Bits: 4294967295
    influenceList: []
  ClampVelocityModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    magnitude:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxis: 0
    inWorldSpace: 0
    multiplyDragByParticleSize: 1
    multiplyDragByParticleVelocity: 1
    dampen: 0
    drag:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  NoiseModule:
    enabled: 0
    strength:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    strengthY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    strengthZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
    frequency: 0.5
    damping: 1
    octaves: 1
    octaveMultiplier: 0.5
    octaveScale: 2
    quality: 2
    scrollSpeed:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remap:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: -1
          inSlope: 0
          outSlope: 2
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 2
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapY:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: -1
          inSlope: 0
          outSlope: 2
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 2
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapZ:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: -1
          inSlope: 0
          outSlope: 2
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 2
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapEnabled: 0
    positionAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    rotationAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    sizeAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  SizeBySpeedModule:
    enabled: 0
    curve:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    range: {x: 0, y: 1}
    separateAxes: 0
  RotationBySpeedModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.7853982
      minScalar: 0.7853982
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
    range: {x: 0, y: 1}
  ColorBySpeedModule:
    enabled: 0
    gradient:
      serializedVersion: 2
      minMaxState: 1
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    range: {x: 0, y: 1}
  CollisionModule:
    enabled: 0
    serializedVersion: 4
    type: 0
    collisionMode: 0
    colliderForce: 0
    multiplyColliderForceByParticleSize: 0
    multiplyColliderForceByParticleSpeed: 0
    multiplyColliderForceByCollisionAngle: 1
    m_Planes: []
    m_Dampen:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_Bounce:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_EnergyLossOnCollision:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    minKillSpeed: 0
    maxKillSpeed: 10000
    radiusScale: 1
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    maxCollisionShapes: 256
    quality: 0
    voxelSize: 0.5
    collisionMessages: 0
    collidesWithDynamic: 1
    interiorCollisions: 0
  TriggerModule:
    enabled: 0
    serializedVersion: 2
    inside: 1
    outside: 0
    enter: 0
    exit: 0
    colliderQueryMode: 0
    radiusScale: 1
    primitives: []
  SubModule:
    serializedVersion: 2
    enabled: 0
    subEmitters:
    - serializedVersion: 3
      emitter: {fileID: 0}
      type: 0
      properties: 0
      emitProbability: 1
  LightsModule:
    enabled: 0
    ratio: 0
    light: {fileID: 0}
    randomDistribution: 1
    color: 1
    range: 1
    intensity: 1
    rangeCurve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    intensityCurve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    maxLights: 20
  TrailModule:
    enabled: 0
    mode: 0
    ratio: 1
    lifetime:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    minVertexDistance: 0.2
    textureMode: 0
    textureScale: {x: 1, y: 1}
    ribbonCount: 1
    shadowBias: 0.5
    worldSpace: 0
    dieWithParticles: 1
    sizeAffectsWidth: 1
    sizeAffectsLifetime: 0
    inheritParticleColor: 1
    generateLightingData: 0
    splitSubEmitterRibbons: 0
    attachRibbonsToTransform: 0
    colorOverLifetime:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    widthOverTrail:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    colorOverTrail:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
  CustomDataModule:
    enabled: 0
    mode0: 0
    vectorComponentCount0: 4
    color0:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    colorLabel0: Color
    vector0_0:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_0: X
    vector0_1:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_1: Y
    vector0_2:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_2: Z
    vector0_3:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_3: W
    mode1: 0
    vectorComponentCount1: 4
    color1:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    colorLabel1: Color
    vector1_0:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_0: X
    vector1_1:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_1: Y
    vector1_2:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_2: Z
    vector1_3:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_3: W
--- !u!4 &536016435
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 536016432}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 7.27, y: 0.443, z: 5.472}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!1 &560597978
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 560597979}
  - component: {fileID: 560597980}
  m_Layer: 0
  m_Name: WAll (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &560597979
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 560597978}
  serializedVersion: 2
  m_LocalRotation: {x: 0.49999997, y: -0.49999997, z: -0.50000006, w: 0.5000001}
  m_LocalPosition: {x: 0, y: 1.9, z: 1.83}
  m_LocalScale: {x: 8.3938, y: 1, z: 6.437201}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1701055343}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: -90}
--- !u!65 &560597980
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 560597978}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1001 &598214967
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1668271605617530842, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: Dialog.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530842, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: 'Dialog.Array.data[0]'
      value: "I\u2019m afraid. I\u2019m afraid. My mind is going. I can feel it...."
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530842, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_Dialog.m_Lines.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530842, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: Dialog_DEPRECATED.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530842, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: 'Dialog_DEPRECATED.Array.data[0]'
      value: "I\u2019m afraid. I\u2019m afraid. My mind is going. I can feel it...."
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530842, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_Dialog.m_Lines.Array.data[0].Text
      value: '"I am putting myself to the fullest possible use, which is all I think
        that any conscious entity can ever hope to do"'
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530843, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530843, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_LocalPosition.x
      value: 5.9050817
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530843, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.075
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530843, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_LocalPosition.z
      value: 16.123
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530843, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530843, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530843, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530843, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530843, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530843, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530843, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1668271605617530847, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
      propertyPath: m_Name
      value: PointOfInterest
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: c1e8f75370d98b64fb70c380bb655984, type: 3}
--- !u!1 &683600437 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8196853866405996373, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
  m_PrefabInstance: {fileID: 162087929}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &683600441
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683600437}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c921f10d2cef435468fb06032f0b42ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Property: _OverlayOpacity
  m_Value: 0
--- !u!95 &683600442
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 683600437}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: b8888dec534db4c4cb6895cf758cd2c2, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &691661737 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8607145438657436085, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
  m_PrefabInstance: {fileID: 1389159341}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &691661738 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
  m_PrefabInstance: {fileID: 1389159341}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &691661739
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 691661737}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d30cf7f8506435e48b475044a49393d6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Camera: {fileID: 923950418}
--- !u!1 &788191051
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 788191053}
  - component: {fileID: 788191052}
  m_Layer: 0
  m_Name: AmbientSound
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!82 &788191052
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 788191051}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: c0c9a407d49459a4d9b57c356c2eb589, type: 3}
  m_Resource: {fileID: 8300000, guid: c0c9a407d49459a4d9b57c356c2eb589, type: 3}
  m_PlayOnAwake: 1
  m_Volume: 0.15
  m_Pitch: 1
  Loop: 1
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!4 &788191053
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 788191051}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 11.904481, y: -2.3548741, z: 14.135003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &833275728 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
  m_PrefabInstance: {fileID: 269121494}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &833275729 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 2618498201448163646, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
  m_PrefabInstance: {fileID: 269121494}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a8a881b5bf711fc499d4b5ff3d004b9f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &840064793
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 840064794}
  - component: {fileID: 840064795}
  m_Layer: 0
  m_Name: WAll
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &840064794
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 840064793}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0.70710677, w: 0.7071068}
  m_LocalPosition: {x: -3.1299949, y: 0, z: 10.62479}
  m_LocalScale: {x: 8.3938, y: 1, z: 28.079393}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1701055343}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: -90}
--- !u!65 &840064795
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 840064793}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!20 &923950418 stripped
Camera:
  m_CorrespondingSourceObject: {fileID: 1079691871999004174, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
  m_PrefabInstance: {fileID: 2004167159893521164}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &949613582
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 8203255208461063176, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 8203255208461063176, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8203255208461063176, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8203255208461063176, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8203255208461063176, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8203255208461063176, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8203255208461063176, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8203255208461063176, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8203255208461063176, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8203255208461063176, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8203255208461063176, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8203255208461063178, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
      propertyPath: m_Name
      value: HECore
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 193bf82098c71a341a2c6f67ffa422b5, type: 3}
--- !u!1001 &1017147256
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 506901168}
    m_Modifications:
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: Name
      value: SpaceshipCorridor
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: ColorTexture
      value: 
      objectReference: {fileID: 2800000, guid: a3512c9a870725243831d71e169a3053, type: 3}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_RenderToColor
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_LightingCapture
      value: 
      objectReference: {fileID: 65569774}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: DepthRenderTexture
      value: 
      objectReference: {fileID: 8400000, guid: 0f9243b5720f5b24a85fc1ab37d8c55f, type: 2}
    - target: {fileID: 8196853866405996203, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: 'm_RuntimeEffects.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 1f4eb235c9a84d94e90567612df7f6fc, type: 2}
    - target: {fileID: 8196853866405996373, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      propertyPath: m_Name
      value: PreRenderedCam
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 8196853866405996373, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 450381534}
    - targetCorrespondingSourceObject: {fileID: 8196853866405996373, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 450381538}
  m_SourcePrefab: {fileID: 100100000, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
--- !u!4 &1017147257 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 8196853866405996202, guid: a65e23e2a032e284dbb05ff91cf093c6, type: 3}
  m_PrefabInstance: {fileID: 1017147256}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1218234836 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
  m_PrefabInstance: {fileID: 220675694}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1272699805
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1218234836}
    m_Modifications:
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalScale.x
      value: 11.649
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalScale.y
      value: 11.649
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalScale.z
      value: 11.649
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalPosition.x
      value: 5.1944156
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.9000001
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1.1756494
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071071
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.7071065
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.00000615698
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.0000061569854
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4237837642253022154, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 9184288872504641143, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
      propertyPath: m_Name
      value: LightCapture (1)
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 5fe95d680a9cabb4bb704f6295ff9250, type: 3}
--- !u!1001 &1389159341
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 171055928}
    m_Modifications:
    - target: {fileID: 1191269246897273220, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.18
      objectReference: {fileID: 0}
    - target: {fileID: 1191269246897273220, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.6600001
      objectReference: {fileID: 0}
    - target: {fileID: 1191269246897273220, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.z
      value: 16.63
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.FieldOfView
      value: 43.60282
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.FarClipPlane
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.ModeOverride
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.NearClipPlane
      value: 0.099999994
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.m_SensorSize.x
      value: 36
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.m_SensorSize.y
      value: 24
      objectReference: {fileID: 0}
    - target: {fileID: 1579692686389723225, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Lens.OrthographicSize
      value: 0.0040000216
      objectReference: {fileID: 0}
    - target: {fileID: 2471370469874519972, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 4294967287
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.FieldOfView
      value: 43.60282
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.FarClipPlane
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.ModeOverride
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.NearClipPlane
      value: 0.2598474
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.OrthographicSize
      value: 0.010393953
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.PhysicalProperties.SensorSize.x
      value: 36
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.PhysicalProperties.SensorSize.y
      value: 24
      objectReference: {fileID: 0}
    - target: {fileID: 3192128901485635612, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: Lens.PhysicalProperties.FocusDistance
      value: 2598.4739
      objectReference: {fileID: 0}
    - target: {fileID: 5130916788458517775, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Size.x
      value: 9.46
      objectReference: {fileID: 0}
    - target: {fileID: 5130916788458517775, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Size.y
      value: 7.68
      objectReference: {fileID: 0}
    - target: {fileID: 5130916788458517775, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Size.z
      value: 6.7
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.x
      value: 9.89
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.6558323
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.z
      value: -3.31
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.x
      value: -3.5663824
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.98057663
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalPosition.z
      value: 9.889662
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9565025
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.13986766
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.25331476
      objectReference: {fileID: 0}
    - target: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.03703623
      objectReference: {fileID: 0}
    - target: {fileID: 7888032800186445521, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      propertyPath: m_Name
      value: CameraHAL
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 5789214400614700604, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 833275728}
    - targetCorrespondingSourceObject: {fileID: 7687138500670737955, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 162087930}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 8607145438657436085, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 691661739}
    - targetCorrespondingSourceObject: {fileID: 2471370469874519972, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1389159343}
  m_SourcePrefab: {fileID: 100100000, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
--- !u!1 &1389159342 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2471370469874519972, guid: 57be7cbfb83c77b4196f49c1698080c1, type: 3}
  m_PrefabInstance: {fileID: 1389159341}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1389159343
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1389159342}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1e3fdca004f2d45fe8abbed571a8abd5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SerializedVersion: 0
  m_OverrideArea: 0
  m_Area: 0
  m_OverrideGenerateLinks: 1
  m_GenerateLinks: 1
  m_IgnoreFromBuild: 0
  m_ApplyToChildren: 1
  m_AffectedAgents: ffffffff
--- !u!20 &1396297104 stripped
Camera:
  m_CorrespondingSourceObject: {fileID: 7702878075839524706, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
  m_PrefabInstance: {fileID: 2004167159893521164}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1701055342
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1701055343}
  m_Layer: 0
  m_Name: Collisions
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1701055343
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1701055342}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 6.129995, y: -2.639999, z: -5.799999}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 112061619}
  - {fileID: 2011700559}
  - {fileID: 840064794}
  - {fileID: 337757318}
  - {fileID: 300825859}
  - {fileID: 560597979}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1773120417
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 3772103242538898809, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3772103242538898809, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_LocalPosition.x
      value: 5.95
      objectReference: {fileID: 0}
    - target: {fileID: 3772103242538898809, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.8
      objectReference: {fileID: 0}
    - target: {fileID: 3772103242538898809, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.54
      objectReference: {fileID: 0}
    - target: {fileID: 3772103242538898809, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3772103242538898809, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3772103242538898809, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3772103242538898809, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3772103242538898809, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3772103242538898809, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3772103242538898809, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3772103242538898811, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
      propertyPath: m_Name
      value: PlayerSpawnPoint
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 455a0d83e7921ab4e9335a2fe24b143f, type: 3}
--- !u!1 &2011700558
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2011700559}
  - component: {fileID: 2011700560}
  m_Layer: 0
  m_Name: WAll
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2011700559
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2011700558}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0.70710677, w: 0.7071068}
  m_LocalPosition: {x: 2.6200051, y: 0, z: 10.62479}
  m_LocalScale: {x: 8.3938, y: 1, z: 28.079393}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1701055343}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: -90}
--- !u!65 &2011700560
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2011700558}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1001 &2004167159893521164
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 16628614788846455, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.598474
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalScale.y
      value: 2.598474
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalScale.z
      value: 2.598474
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalPosition.x
      value: 5.87
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.43
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1.2
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.70710677
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 529391537253409520, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 603493172990120182, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_Convex
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 820162462814642529, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 870063757934868554, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_Name
      value: SciFiCorridor (1)
      objectReference: {fileID: 0}
    - target: {fileID: 870063757934868554, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 1079691871999004174, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: far clip plane
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 1314016536644511637, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 2135361571967774472, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 2207578903342635105, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 2476009390262215181, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 2490751506610525406, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 2573647268717115832, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 2742132191996022532, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 3522859692158882646, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 3545228465135516277, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 3633090056549305115, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 3872925275389603123, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 4038845321446842027, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_UsePipelineSettings
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4038845321446842027, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_AdditionalLightsShadowResolutionTier
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4177379315306628710, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_AdditionalLightsShadowResolutionTier
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6535684334416866365, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 6672228601203633896, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 6695198029700934376, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_Range
      value: 12
      objectReference: {fileID: 0}
    - target: {fileID: 6695198029700934376, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_Intensity
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6695198029700934376, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_BounceIntensity
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7634941376606829616, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 7702878075839524706, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: far clip plane
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 8367377486605567230, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 8571812868858012712, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_StaticEditorFlags
      value: 2147483639
      objectReference: {fileID: 0}
    - target: {fileID: 9047633754477198092, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_Intensity
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 9047633754477198092, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_BounceIntensity
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9047633754477198092, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
      propertyPath: m_Shadows.m_NormalBias
      value: 0.1
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 603493172990120182, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 504f8e392bcfd7d449b5c2756b46b1f5, type: 3}
--- !u!1001 &6450028867888385506
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 2730150273385471115, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3468066335942b34cb7612e75fd7023a, type: 2}
    - target: {fileID: 4704861257608871491, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3468066335942b34cb7612e75fd7023a, type: 2}
    - target: {fileID: 6143599033697081947, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_Name
      value: Fan
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.722845
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.722845
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalScale.z
      value: 1.0065721
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalPosition.x
      value: 8.4604
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.382
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.27
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071066
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.000000026692538
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.70710695
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.00000002669255
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 6776125689553928417, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: d794a5dfc645aef4aa8bb83c88dfccce, type: 3}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 598214967}
  - {fileID: 2004167159893521164}
  - {fileID: 6450028867888385506}
  - {fileID: 949613582}
  - {fileID: 536016435}
  - {fileID: 1773120417}
  - {fileID: 171055928}
  - {fileID: 788191053}
  - {fileID: 1701055343}
