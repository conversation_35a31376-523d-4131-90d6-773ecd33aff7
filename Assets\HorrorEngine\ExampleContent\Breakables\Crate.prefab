%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2863884025175984814
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2712583037526613849}
  m_Layer: 0
  m_Name: Content
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2712583037526613849
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2863884025175984814}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.32, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7774009177584177061}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7774009177584177056
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7774009177584177061}
  - component: {fileID: 7774009177584177062}
  - component: {fileID: 6540256518361563479}
  - component: {fileID: 4461426752851476425}
  - component: {fileID: 7126364995445281142}
  m_Layer: 0
  m_Name: Crate
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7774009177584177061
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7774009177584177056}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7774009177646485539}
  - {fileID: 754373315778391552}
  - {fileID: 3860083962609631275}
  - {fileID: 2712583037526613849}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7774009177584177062
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7774009177584177056}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ec3a046b976ab440aac443f4b308f2d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Infinite: 0
  Invulnerable: 0
  Max: 1
  Min: 0
  Value: 1
  InitialValue: 0
  OnHealthAltered:
    m_PersistentCalls:
      m_Calls: []
  OnHealthDecreased:
    m_PersistentCalls:
      m_Calls: []
  OnDeath:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 86626278558495930}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 4528955761509259921}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
      - m_Target: {fileID: 2863884025175984814}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
      - m_Target: {fileID: 7774009177646485564}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 7126364995445281142}
        m_TargetAssemblyTypeName: HorrorEngine.ObjectInstantiator, Assembly-CSharp
        m_MethodName: Instantiate
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  OnLoadedDead:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 86626278558495930}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 4528955761509259921}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
      - m_Target: {fileID: 7774009177646485564}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 2863884025175984814}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
--- !u!114 &6540256518361563479
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7774009177584177056}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9291977c9f2a14a5099c6a295b1a2eee, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Id: 063380b0-2d6d-45b9-a0f2-85126b2cfce8
  IsUniqueInstance: 0
--- !u!114 &4461426752851476425
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7774009177584177056}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70caf85336dfd4b369928545b052d32e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ApplySavedTransform: 1
--- !u!114 &7126364995445281142
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7774009177584177056}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0c505e6b38dd76641967185c92bbc153, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Prefab: {fileID: 2944496999592498191, guid: 099dc568afc9ce4419dd446dc898bdef, type: 3}
  Settings:
    Parent: {fileID: 7774009177584177061}
    Socket: {fileID: 0}
    Position: {x: 0, y: 0, z: 0}
    Rotation: {x: 0, y: 0, z: 0}
    Scale: 1
    IsLocal: 1
    InheritsRotation: 0
    DetachFromParent: 1
--- !u!1 &7774009177646485564
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7774009177646485539}
  - component: {fileID: 7774009177646485537}
  - component: {fileID: 7774009177646485538}
  m_Layer: 9
  m_Name: Damageable
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7774009177646485539
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7774009177646485564}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7774009177584177061}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &7774009177646485537
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7774009177646485564}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.75, y: 0.75, z: 1.15}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &7774009177646485538
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7774009177646485564}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f82791e57d8266478186984ade90ab0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: {fileID: 11400000, guid: 90872621620408642a53542a877e0e5e, type: 2}
  OnPreDamage:
    m_PersistentCalls:
      m_Calls: []
  OnDamage:
    m_PersistentCalls:
      m_Calls: []
  Priority: 0
--- !u!1001 &1005054739198265835
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7774009177584177061}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.3104546
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.3104546
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.3104546
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.011
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 5cf282e0e7e87bb4bb1dae6d88a39197, type: 2}
    - target: {fileID: 919132149155446097, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      propertyPath: m_Name
      value: Crate
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4105216349482988119}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8262066852759030286}
  m_SourcePrefab: {fileID: 100100000, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
--- !u!1 &86626278558495930 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
  m_PrefabInstance: {fileID: 1005054739198265835}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &4105216349482988119
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 86626278558495930}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 2.0561297, y: 2.0561297, z: 3.3069851}
  m_Center: {x: 0, y: 0, z: 0.0000076293945}
--- !u!208 &8262066852759030286
NavMeshObstacle:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 86626278558495930}
  m_Enabled: 1
  serializedVersion: 3
  m_Shape: 1
  m_Extents: {x: 1.028, y: 1.028, z: 1.6530001}
  m_MoveThreshold: 0.1
  m_Carve: 1
  m_CarveOnlyStationary: 0
  m_Center: {x: 0, y: 0, z: 0}
  m_TimeToStationary: 0.5
--- !u!4 &754373315778391552 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: dc0b25adcfb907a43804c5885f33278a, type: 3}
  m_PrefabInstance: {fileID: 1005054739198265835}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3610616505327871936
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7774009177584177061}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.3104546
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.3104546
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.3104546
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.011
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 5cf282e0e7e87bb4bb1dae6d88a39197, type: 2}
    - target: {fileID: 919132149155446097, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_Name
      value: CrateBroken
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1786738259647438593}
  m_SourcePrefab: {fileID: 100100000, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
--- !u!4 &3860083962609631275 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
  m_PrefabInstance: {fileID: 3610616505327871936}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &4528955761509259921 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 166060ee6ba51554d9888a8a7fc62a9f, type: 3}
  m_PrefabInstance: {fileID: 3610616505327871936}
  m_PrefabAsset: {fileID: 0}
--- !u!208 &1786738259647438593
NavMeshObstacle:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4528955761509259921}
  m_Enabled: 1
  serializedVersion: 3
  m_Shape: 1
  m_Extents: {x: 1.028, y: 1.028, z: 1.6530001}
  m_MoveThreshold: 0.1
  m_Carve: 1
  m_CarveOnlyStationary: 0
  m_Center: {x: 0, y: 0, z: 0}
  m_TimeToStationary: 0.5
