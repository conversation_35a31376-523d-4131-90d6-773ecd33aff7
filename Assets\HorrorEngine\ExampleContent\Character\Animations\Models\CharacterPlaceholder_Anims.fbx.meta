fileFormatVersion: 2
guid: ccbf25a14979018408ab2166f6cf4f9e
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable:
  - first:
      74: -7361427651036151181
    second: Idle
  - first:
      74: -8274178093218619139
    second: Knife<PERSON>ttack
  - first:
      74: -5124320824457396573
    second: Shooting
  - first:
      74: -2442247154743843185
    second: KnifeReady
  - first:
      74: 6291245561910097119
    second: ShootingReady
  externalObjects: {}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 0
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Idle
      takeName: rig|Idle
      internalID: 0
      firstFrame: 0
      lastFrame: 49
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeAttack
      takeName: rig|KnifeAttack
      internalID: 0
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Shooting
      takeName: rig|Shooting
      internalID: 0
      firstFrame: 0
      lastFrame: 31
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeReady
      takeName: rig|KnifeAttack
      internalID: 0
      firstFrame: 23
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingReady
      takeName: rig|Shooting
      internalID: 0
      firstFrame: 13
      lastFrame: 14
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Death
      takeName: rig|Death
      internalID: -5282910460489992114
      firstFrame: 0
      lastFrame: 69
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Hurt
      takeName: rig|Hurt
      internalID: 4844758029973935614
      firstFrame: 0
      lastFrame: 14
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Pickup
      takeName: rig|Pickup
      internalID: -6462006518987283793
      firstFrame: 0
      lastFrame: 44
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: WalkingBackwards
      takeName: rig|WalkingBackwards
      internalID: -2714271014747795561
      firstFrame: 0
      lastFrame: 25
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events:
      - time: 0.37546408
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.8022957
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Walking
      takeName: rig|Walking
      internalID: 6294929670863694331
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events:
      - time: 0.14285718
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.6292272
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Running
      takeName: rig|Running
      internalID: -3366119745786397064
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events:
      - time: 0.104018606
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.19093443
        functionName: PlayVFX
        data: FootstepR
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.59038866
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.6822909
        functionName: PlayVFX
        data: FootstepL
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingUp
      takeName: rig|ShootingUp
      internalID: 4802321216126950569
      firstFrame: 0
      lastFrame: 14
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingDown
      takeName: ShootingDown
      internalID: -8230935577045453037
      firstFrame: 0
      lastFrame: 14
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingReadyUp
      takeName: rig|ShootingUp
      internalID: 1201474078089654996
      firstFrame: 13
      lastFrame: 14
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeAttackUp
      takeName: rig|KnifeAttackUp
      internalID: 521954011688344991
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeAttackDown
      takeName: rig|KnifeAttackDown
      internalID: -3135095980022291064
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeReadyDown
      takeName: rig|KnifeAttackDown
      internalID: 1657828558177769340
      firstFrame: 23
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeReadyUp
      takeName: rig|KnifeAttackUp
      internalID: -6999577665805043283
      firstFrame: 23
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingReadyDown
      takeName: ShootingDown
      internalID: -6435662007456853337
      firstFrame: 13
      lastFrame: 14
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingReload
      takeName: rig|Reload
      internalID: 331073901332383093
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: WalkingHurt
      takeName: rig|WalkingHurt
      internalID: 7534333973487723809
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events:
      - time: 0.17142858
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.65779865
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: RunningHurt
      takeName: rig|RunningHurt
      internalID: -919690005892133193
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events:
      - time: 0.156962
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.23291138
        functionName: PlayVFX
        data: FootstepR
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.6421941
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.7434599
        functionName: PlayVFX
        data: FootstepL
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: IdleHurt
      takeName: rig|IdleHurt
      internalID: -3828155435326560578
      firstFrame: 0
      lastFrame: 49
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Turn180
      takeName: rig|Turn180
      internalID: 5935208927122241369
      firstFrame: 0
      lastFrame: 29
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgun
      takeName: rig|ShootingShotgun
      internalID: -6534805508056839715
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgunReady
      takeName: rig|ShootingShotgun
      internalID: 4482853421334786166
      firstFrame: 23
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgunUp
      takeName: rig|ShootingShotgunUp
      internalID: 7853541250257003928
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgunUpReady
      takeName: rig|ShootingShotgunUp
      internalID: 6937789355426993069
      firstFrame: 23
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgunDown
      takeName: rig|ShootingShotgunDown
      internalID: 8980005541725699608
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgunDownReady
      takeName: rig|ShootingShotgunDown
      internalID: -182666804023253555
      firstFrame: 23
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ShootingShotgunReload
      takeName: rig|ShootingShotgunReload
      internalID: -5833674769716960917
      firstFrame: 0
      lastFrame: 59
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ReloadAssault
      takeName: rig|ReloadAssault
      internalID: -6417410876690347977
      firstFrame: 0
      lastFrame: 36
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Pushing
      takeName: Pushing
      internalID: 5184292631486710248
      firstFrame: 0
      lastFrame: 21
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves:
      - name: PushStrength
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: -0.003277868
            value: 1
            inSlope: 7.1337457
            outSlope: 7.1337457
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0.027229529
          - serializedVersion: 3
            time: 0.50217396
            value: 0.99846846
            inSlope: -6.393081
            outSlope: -6.393081
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.05912153
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 3
          postInfinity: 3
          rotationOrder: 4
      events:
      - time: 0.058419216
        functionName: TriggerEvent
        data: StartPush
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PushingPose
      takeName: Pushing
      internalID: 4159648686747164291
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ClimbLadderEnter
      takeName: rig|ClimbLadderEnter
      internalID: -7384119234241726787
      firstFrame: 0
      lastFrame: 19
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves:
      - name: ClimbAttachment
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 3
          postInfinity: 3
          rotationOrder: 4
      - name: ClimbProgress
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.20341209
            value: 0
            inSlope: 0
            outSlope: 6.8765273
            tangentMode: 1
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.08344458
          - serializedVersion: 3
            time: 0.4
            value: 0
            inSlope: -1.7229731
            outSlope: 7.332206
            tangentMode: 1
            weightedMode: 0
            inWeight: 0.27403206
            outWeight: 0.08177077
          - serializedVersion: 3
            time: 0.68456346
            value: 0.5
            inSlope: 0.08598626
            outSlope: 0.08598626
            tangentMode: 34
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 3
          postInfinity: 3
          rotationOrder: 4
      events:
      - time: 0.48456374
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.9406207
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.95049506
        functionName: TriggerEvent
        data: ClimbEnterEnd
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ClimbLadderLoop
      takeName: ClimbLadderLoop
      internalID: 6687303226208316012
      firstFrame: 0
      lastFrame: 19
      wrapMode: 0
      orientationOffsetY: 0
      level: 0.25
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves:
      - name: ClimbProgress
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.35
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.50859153
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.65
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.9
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 3
          postInfinity: 3
          rotationOrder: 4
      - name: ClimbAttachment
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 2
          postInfinity: 2
          rotationOrder: 4
      events:
      - time: 0
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.5315436
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ClimbLadderExit
      takeName: ClimbLadderExit
      internalID: -4419430799034313034
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 1.1
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves:
      - name: ClimbAttachment
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.6
            value: 1
            inSlope: -0.013972825
            outSlope: -0.033817783
            tangentMode: 1
            weightedMode: 0
            inWeight: 0.22480892
            outWeight: 0.22703421
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: -0.02217086
            outSlope: -0.02217086
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.1758529
            outWeight: 0
          preInfinity: 2
          postInfinity: 2
          rotationOrder: 4
      - name: ClimbProgress
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.16240157
            value: 0
            inSlope: 0
            outSlope: 3.4056344
            tangentMode: 1
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.025973981
          - serializedVersion: 3
            time: 0.19407293
            value: 1.0002112
            inSlope: 1.915031
            outSlope: 0.022370448
            tangentMode: 1
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 1
          - serializedVersion: 3
            time: 0.34110388
            value: 1.0010269
            inSlope: 0.05761005
            outSlope: 0.05761005
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.96248496
          - serializedVersion: 3
            time: 0.6151702
            value: 0.0006255457
            inSlope: -0.0003598433
            outSlope: -0.0003598433
            tangentMode: 1
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.6544526
            value: 0.9994099
            inSlope: -0.0006102226
            outSlope: -0.0006102226
            tangentMode: 1
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.77397335
            value: 1.0000503
            inSlope: 0.003299308
            outSlope: 0.003299308
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.4404272
          - serializedVersion: 3
            time: 0.9
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 3
          postInfinity: 3
          rotationOrder: 4
      events:
      - time: 0.96451193
        functionName: TriggerEvent
        data: ClimbExitEnd
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.18255034
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.57852346
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: DropLadderEnter
      takeName: DropLadder
      internalID: 4028210526346528696
      firstFrame: 0
      lastFrame: 31
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves:
      - name: ClimbAttachment
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0.003254408
            outSlope: 0.003254408
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.3132423
            value: 0.0010194182
            inSlope: 0.003254408
            outSlope: 3.2136078
            tangentMode: 69
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.6227928
            value: 0.9957933
            inSlope: 3.2136078
            outSlope: 0.01115227
            tangentMode: 69
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0.01115227
            outSlope: 0.01115227
            tangentMode: 34
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0
          preInfinity: 2
          postInfinity: 2
          rotationOrder: 4
      - name: ClimbProgress
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: -0.0007141477
            outSlope: -0.0007141477
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.36403465
            value: -0.0002599745
            inSlope: -0.0007141477
            outSlope: 13.374993
            tangentMode: 69
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.43859586
            value: 0.9969957
            inSlope: 13.374993
            outSlope: 0.005351426
            tangentMode: 69
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0.005351426
            outSlope: 0.005351426
            tangentMode: 1
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          preInfinity: 2
          postInfinity: 2
          rotationOrder: 4
      events:
      - time: 0.88410586
        functionName: TriggerEvent
        data: ClimbEnterEnd
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.53825504
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: DropLadderLoop
      takeName: DropLadder
      internalID: 792574518632363067
      firstFrame: 32
      lastFrame: 33
      wrapMode: 0
      orientationOffsetY: 180
      level: -0.04
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves:
      - name: ClimbAttachment
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 2
          postInfinity: 2
          rotationOrder: 4
      - name: ClimbProgress
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 2
          postInfinity: 2
          rotationOrder: 4
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: DropLadderExit
      takeName: DropLadder
      internalID: -8263215989286157946
      firstFrame: 40
      lastFrame: 72
      wrapMode: 0
      orientationOffsetY: 0
      level: -2.24
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves:
      - name: ClimbAttachment
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.13766405
            value: 1
            inSlope: -0
            outSlope: -3.669491
            tangentMode: 69
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.4101814
            value: 0
            inSlope: -3.669491
            outSlope: 0
            tangentMode: 69
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 34
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0
          preInfinity: 2
          postInfinity: 2
          rotationOrder: 4
      - name: ClimbProgress
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 3
          postInfinity: 3
          rotationOrder: 4
      events:
      - time: 0
        functionName: TriggerEvent
        data: ClearSurfaceOverride
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.026344232
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.027375033
        functionName: PlayVFX
        data: FootstepL
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.04019553
        functionName: PlayVFX
        data: FootstepR
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.04429294
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 1
        functionName: TriggerEvent
        data: ClimbExitEnd
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: ClimbVaulting
      takeName: Vaulting
      internalID: 9174578235125474360
      firstFrame: 0
      lastFrame: 36
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves:
      - name: ClimbAttachment
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 25.17494
            outSlope: 25.17494
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0.049212597
          - serializedVersion: 3
            time: 0.1
            value: 1
            inSlope: -0.05400004
            outSlope: 0.00004757856
            tangentMode: 65
            weightedMode: 0
            inWeight: 0.9015748
            outWeight: 0.0820939
          - serializedVersion: 3
            time: 0.19520997
            value: 1.0000045
            inSlope: -0.08561149
            outSlope: -0.08561149
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.15165658
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: -0.24400225
            outSlope: -0.24400225
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.162575
            outWeight: 0
          preInfinity: 2
          postInfinity: 2
          rotationOrder: 4
      - name: ClimbProgress
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.15
            value: -0.0024461746
            inSlope: -0.01630783
            outSlope: 15.066173
            tangentMode: 69
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.21653543
            value: 0.9999881
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.43339893
            value: 1.0048804
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 3
          postInfinity: 3
          rotationOrder: 4
      events:
      - time: 0.1489933
        functionName: TriggerEvent
        data: ClimbEnterEnd
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.9946308
        functionName: TriggerEvent
        data: ClimbExitEnd
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.40402684
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.90067106
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: DropVaulting
      takeName: DropVaulting
      internalID: -4400378899001343355
      firstFrame: 0
      lastFrame: 27
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves:
      - name: ClimbAttachment
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.19849083
            value: 1
            inSlope: -0.18549375
            outSlope: -3.1424482
            tangentMode: 1
            weightedMode: 0
            inWeight: 0.3305785
            outWeight: 0.06958657
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0.030388042
            outSlope: 0.030388042
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.119934484
            outWeight: 0
          preInfinity: 3
          postInfinity: 3
          rotationOrder: 4
      - name: ClimbProgress
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.21459307
            value: 0
            inSlope: -0
            outSlope: 8.07993
            tangentMode: 5
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.31824142
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.5
            value: 1
            inSlope: -0
            outSlope: -1.2714976
            tangentMode: 5
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.51
            value: 0
            inSlope: -2.5622652
            outSlope: 0
            tangentMode: 65
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 3
          postInfinity: 3
          rotationOrder: 4
      events:
      - time: 0.40402684
        functionName: TriggerEvent
        data: ClimbEnterEnd
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.9812081
        functionName: TriggerEvent
        data: ClimbExitEnd
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.49127516
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.51812077
        functionName: PlaySFX
        data: Footstep
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.45100668
        functionName: PlayVFX
        data: FootstepR
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.55929726
        functionName: PlayVFX
        data: FootstepL
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: VaultingOn
      takeName: rig|VaultingOn
      internalID: -9209154002952655788
      firstFrame: 0
      lastFrame: 39
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves:
      - name: ClimbAttachment
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 4
            outSlope: 4
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.25
            value: 1
            inSlope: -0.046520185
            outSlope: 0.106711864
            tangentMode: 1
            weightedMode: 0
            inWeight: 0.20931756
            outWeight: 0.18285215
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0.06556699
            outSlope: 0.06556699
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.1832222
            outWeight: 0
          preInfinity: 2
          postInfinity: 2
          rotationOrder: 4
      - name: ClimbProgress
        curve:
          serializedVersion: 2
          curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 1
            outSlope: 1
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 1
            outSlope: 1
            tangentMode: 34
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          preInfinity: 2
          postInfinity: 2
          rotationOrder: 4
      events:
      - time: 0.29480752
        functionName: TriggerEvent
        data: ClimbEnterEnd
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.96851647
        functionName: TriggerEvent
        data: ClimbExitEnd
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: GrabbedInto
      takeName: rig|Grabbed
      internalID: 7533255309600893707
      firstFrame: 0
      lastFrame: 11
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: GrabbedLoop
      takeName: rig|Grabbed
      internalID: -7002173670783086910
      firstFrame: 11
      lastFrame: 39
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: GrabbedAttack
      takeName: rig|GrabbedStab
      internalID: 4205108182848074419
      firstFrame: 0
      lastFrame: 54
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: GrabbedHurt
      takeName: rig|Hurt
      internalID: -6944571868683677145
      firstFrame: 3
      lastFrame: 8.6
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 1
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: GrabbedBack
      takeName: rig|GrabbedBack
      internalID: 5907618463459243380
      firstFrame: 0
      lastFrame: 45
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: GrabbedBackHurt
      takeName: rig|GrabbedBackHurt
      internalID: -5056888090625666039
      firstFrame: 0
      lastFrame: 15
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: GrabbedBackRelease
      takeName: rig|GrabbedBackRelease
      internalID: 5973700206801827936
      firstFrame: 0
      lastFrame: 30
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 1
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events:
      - time: 0.5041975
        functionName: TriggerEvent
        data: StartGrabReleaseAttack
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: GrabbedRelease
      takeName: rig|GrabbedRelease
      internalID: 3087659847127488351
      firstFrame: 0
      lastFrame: 40
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events:
      - time: 0.18814814
        functionName: TriggerEvent
        data: StartGrabReleaseAttack
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeAttack2
      takeName: rig|KnifeAttack2
      internalID: -1596590073958076093
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeAttack2Down
      takeName: rig|KnifeAttack2Down
      internalID: -8749515495061144647
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeAttack2Up
      takeName: rig|KnifeAttack2Up
      internalID: -4569576023107879182
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeAttack3
      takeName: rig|KnifeAttack3
      internalID: -1731502345133438220
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeAttack3Up
      takeName: rig|KnifeAttack3Up
      internalID: -6071938096116171456
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: KnifeAttack3Down
      takeName: rig|KnifeAttack3Down
      internalID: -1627042783183169959
      firstFrame: 0
      lastFrame: 24
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: PickupGround
      takeName: PickupGround
      internalID: 4426671336254648458
      firstFrame: 0
      lastFrame: 49
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 1
      keepOriginalOrientation: 1
      keepOriginalPositionY: 0
      keepOriginalPositionXZ: 0
      heightFromFeet: 1
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 0
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: pelvis
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thigh_left
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: thigh_right
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: shin.L
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: shin.R
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: foot_left
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: foot_right
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: upper_arm_left
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: upper_arm_right
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: forearm_left
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: forearm_right
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: hand_left
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: hand_right
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: CharacterPlaceholder_Anims(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Character
      parentName: CharacterPlaceholder_Anims(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 100, y: 100, z: 100}
    - name: rig
      parentName: CharacterPlaceholder_Anims(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 100, y: 100, z: 100}
    - name: pelvis
      parentName: rig
      position: {x: 3.8758109e-19, y: -0.00017375636, z: 0.009615077}
      rotation: {x: 0.7904112, y: -0.008365719, z: -0.0064825485, w: 0.6124853}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: spine
      parentName: pelvis
      position: {x: -0.00007394112, y: 0.0014151843, z: 0.000023409617}
      rotation: {x: -0.15880963, y: 0.0010477555, z: 0.0047421623, w: 0.9872973}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: head
      parentName: spine
      position: {x: 1.641456e-10, y: 0.005644484, z: 0.00035330228}
      rotation: {x: 0.07011087, y: -0.0066225766, z: -0.0067733405, w: 0.9974942}
      scale: {x: 1, y: 1, z: 1}
    - name: head_end
      parentName: head
      position: {x: -0, y: 0.0019949002, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: upper_arm_left
      parentName: spine
      position: {x: -0.0022460774, y: 0.004328443, z: -0.00021312795}
      rotation: {x: 0.00022161007, y: -0.0014468377, z: 0.71012276, w: 0.7040764}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000005}
    - name: forearm_left
      parentName: upper_arm_left
      position: {x: -3.702007e-10, y: 0.0029911378, z: -1.3038516e-10}
      rotation: {x: 0.030966878, y: -0.16247027, z: -0.0018471864, w: 0.9862257}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.9999999}
    - name: hand_left
      parentName: forearm_left
      position: {x: -1.8626451e-11, y: 0.003541589, z: 0}
      rotation: {x: -0.09794958, y: 0.20545067, z: 0.010444815, w: 0.9736975}
      scale: {x: 1, y: 0.99999994, z: 0.9999999}
    - name: hand_left_end
      parentName: hand_left
      position: {x: -0, y: 0.0013580137, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: upper_arm_right
      parentName: spine
      position: {x: 0.002247027, y: 0.004327949, z: -0.00021763208}
      rotation: {x: 0.0011562109, y: -0.0009272283, z: 0.7171885, w: -0.6968777}
      scale: {x: 0.99999887, y: 0.99999964, z: 0.9999985}
    - name: forearm_right
      parentName: upper_arm_right
      position: {x: -6.891787e-10, y: 0.0029911362, z: -6.519258e-11}
      rotation: {x: 0.028940367, y: -0.110704266, z: -0.011285007, w: 0.9933679}
      scale: {x: 0.9999999, y: 0.99999994, z: 1}
    - name: hand_right
      parentName: forearm_right
      position: {x: 5.5879353e-11, y: 0.0035504433, z: 2.2351741e-10}
      rotation: {x: -0.051905155, y: -0.043741196, z: 0.004531912, w: 0.99768335}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: hand_right_end
      parentName: hand_right
      position: {x: -0, y: 0.0013485765, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: thigh_left
      parentName: pelvis
      position: {x: -0.00094195514, y: 0.0006101045, z: -0.000024448498}
      rotation: {x: 0.9880786, y: -0.044036746, z: 0.056993756, w: 0.13606322}
      scale: {x: 0.9999996, y: 0.99999994, z: 0.9999994}
    - name: shin.L
      parentName: thigh_left
      position: {x: -8.8475643e-11, y: 0.0051845694, z: 0}
      rotation: {x: 0.010674199, y: -0.05869372, z: -0.03527004, w: 0.99759567}
      scale: {x: 1, y: 1.0000001, z: 1.0000001}
    - name: foot_left
      parentName: shin.L
      position: {x: 7.363269e-11, y: 0.004244388, z: 3.288733e-11}
      rotation: {x: -0.55357194, y: 0.18458584, z: 0.08092479, w: 0.80804545}
      scale: {x: 1, y: 0.9999999, z: 0.99999994}
    - name: foot_left_end
      parentName: foot_left
      position: {x: -0, y: 0.0020217788, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: thigh_right
      parentName: pelvis
      position: {x: 0.0009419551, y: 0.0006101045, z: -0.00002444853}
      rotation: {x: 0.9890954, y: 0.047179207, z: -0.026015755, w: 0.13706791}
      scale: {x: 0.9999996, y: 1, z: 0.99999934}
    - name: shin.R
      parentName: thigh_right
      position: {x: -5.995389e-11, y: 0.0051845675, z: -3.958121e-11}
      rotation: {x: -0.034271665, y: 0.006975283, z: 0.040612303, w: 0.9985627}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: foot_right
      parentName: shin.R
      position: {x: 1.16124284e-10, y: 0.004244388, z: -1.0550138e-11}
      rotation: {x: -0.5064177, y: -0.15483351, z: -0.07281417, w: 0.8451425}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
    - name: foot_right_end
      parentName: foot_right
      position: {x: -0, y: 0.0020217788, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.831
    foreArmTwist: 0.695
    upperLegTwist: 0.813
    legTwist: 0.541
    armStretch: 0.125
    legStretch: 0.07
    feetSpacing: 0.129
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 1
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
