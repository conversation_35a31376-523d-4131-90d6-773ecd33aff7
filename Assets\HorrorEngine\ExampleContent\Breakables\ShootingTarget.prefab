%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &4236355165339218789
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5090555448686850199}
  - component: {fileID: 4771578785463249638}
  - component: {fileID: 3554391895576043053}
  - component: {fileID: 8147025782758585360}
  m_Layer: 9
  m_Name: Damageable
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5090555448686850199
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4236355165339218789}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7165413206493946302}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &4771578785463249638
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4236355165339218789}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &3554391895576043053
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4236355165339218789}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f82791e57d8266478186984ade90ab0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Type: {fileID: 11400000, guid: 90872621620408642a53542a877e0e5e, type: 2}
  OnPreDamage:
    m_PersistentCalls:
      m_Calls: []
  OnDamage:
    m_PersistentCalls:
      m_Calls: []
  Priority: 0
--- !u!114 &8147025782758585360
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4236355165339218789}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3eafa02a1ebd7df43824c652e40d3a14, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ImpactVFX: {fileID: 2944496999592498191, guid: 099dc568afc9ce4419dd446dc898bdef, type: 3}
  m_ImpactVFXPosition: 1
  OnlyOnDeath: 0
--- !u!1 &6218342566543281750
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7165413206493946302}
  - component: {fileID: 6942393468896855600}
  m_Layer: 0
  m_Name: ShootingTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7165413206493946302
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6218342566543281750}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5848848161291059131}
  - {fileID: 5090555448686850199}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6942393468896855600
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6218342566543281750}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ec3a046b976ab440aac443f4b308f2d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Infinite: 0
  Invulnerable: 0
  Max: 1
  Min: 0
  Value: 1
  InitialValue: 0
  OnHealthAltered:
    m_PersistentCalls:
      m_Calls: []
  OnHealthDecreased:
    m_PersistentCalls:
      m_Calls: []
  OnDeath:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6218342566543281750}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  OnLoadedDead:
    m_PersistentCalls:
      m_Calls: []
--- !u!1001 &6242535641617160272
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7165413206493946302}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 5cd208288d8132c4cb522841ea337895, type: 2}
    - target: {fileID: 919132149155446097, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
      propertyPath: m_Name
      value: Target
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
--- !u!4 &5848848161291059131 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: 535a8dcf529094f46b3f017d8e6e5255, type: 3}
  m_PrefabInstance: {fileID: 6242535641617160272}
  m_PrefabAsset: {fileID: 0}
