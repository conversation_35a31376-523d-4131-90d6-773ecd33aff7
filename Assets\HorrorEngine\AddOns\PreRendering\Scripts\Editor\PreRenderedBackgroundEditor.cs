using UnityEngine;
using UnityEditor;
using Unity.Cinemachine;

namespace PreRenderBackgrounds
{
    [CustomEditor(typeof(PreRenderedBackground))]
    public class PreRenderedBackgroundEditor : Editor
    {
        [SerializeField] private Material m_RenderBgMaterial;
        [SerializeField] private Shader m_Shader;
        
        private float m_Size = 250;

        // --------------------------------------------------------------------

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            PreRenderedBackground prBg = target as PreRenderedBackground;
            CinemachineCamera virtualCam = prBg.GetComponentInParent<CinemachineCamera>(true);

            if (!virtualCam)
            {
                EditorGUILayout.HelpBox("Parent virtual camera not found", MessageType.Error);
                return;
            }

            EditorGUILayout.Separator();
      
            if (prBg.ColorTexture || prBg.FinalRenderTexture)
            {
                EditorGUILayout.BeginHorizontal();
                GUILayout.Box("", GUILayout.Height(m_Size));
                EditorGUILayout.EndHorizontal();
                Rect scale = GUILayoutUtility.GetLastRect();

                if (prBg.ColorTexture)
                    EditorGUI.DrawTextureTransparent(scale, prBg.ColorTexture, ScaleMode.ScaleToFit);
                else 
                    EditorGUI.DrawTextureTransparent(scale, prBg.FinalRenderTexture, ScaleMode.ScaleToFit);
            }

        }


    }
}
